"use strict";function e(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var u=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,u.get?u:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}Object.defineProperty(exports,"__esModule",{value:!0});var t=e(require("react"));function r(e,t){"function"==typeof e?e(t):null!=e&&(e.current=t)}exports.useEffectfulState=function(e,u=[],n){const[c,f]=t.useState();return t.useLayoutEffect((()=>{const t=e();return f(t),r(n,t),()=>r(n,null)}),u),c};
