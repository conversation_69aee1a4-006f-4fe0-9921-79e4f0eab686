"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var t=require("three"),e=require("react"),n=require("../core/shaderMaterial.cjs.js");function a(t){if(t&&t.__esModule)return t;var e=Object.create(null);return t&&Object.keys(t).forEach((function(n){if("default"!==n){var a=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,a.get?a:{enumerable:!0,get:function(){return t[n]}})}})),e.default=t,Object.freeze(e)}var r=a(t),o=a(e);const i={uniforms:{strokeOpacity:1,fillOpacity:.25,fillMix:0,thickness:.05,colorBackfaces:!1,dashInvert:!0,dash:!1,dashRepeats:4,dashLength:.5,squeeze:!1,squeezeMin:.2,squeezeMax:1,stroke:new r.Color("#ff0000"),backfaceStroke:new r.Color("#0000ff"),fill:new r.Color("#00ff00")},vertex:"\n\t  attribute vec3 barycentric;\n\t\n\t\tvarying vec3 v_edges_Barycentric;\n\t\tvarying vec3 v_edges_Position;\n\n\t\tvoid initWireframe() {\n\t\t\tv_edges_Barycentric = barycentric;\n\t\t\tv_edges_Position = position.xyz;\n\t\t}\n\t  ",fragment:"\n\t\t#ifndef PI\n\t  \t#define PI 3.1415926535897932384626433832795\n\t\t#endif\n  \n\t  varying vec3 v_edges_Barycentric;\n\t  varying vec3 v_edges_Position;\n  \n\t  uniform float strokeOpacity;\n\t  uniform float fillOpacity;\n\t  uniform float fillMix;\n\t  uniform float thickness;\n\t  uniform bool colorBackfaces;\n  \n\t  // Dash\n\t  uniform bool dashInvert;\n\t  uniform bool dash;\n\t  uniform bool dashOnly;\n\t  uniform float dashRepeats;\n\t  uniform float dashLength;\n  \n\t  // Squeeze\n\t  uniform bool squeeze;\n\t  uniform float squeezeMin;\n\t  uniform float squeezeMax;\n  \n\t  // Colors\n\t  uniform vec3 stroke;\n\t  uniform vec3 backfaceStroke;\n\t  uniform vec3 fill;\n  \n\t  // This is like\n\t  float wireframe_aastep(float threshold, float dist) {\n\t\t  float afwidth = fwidth(dist) * 0.5;\n\t\t  return smoothstep(threshold - afwidth, threshold + afwidth, dist);\n\t  }\n  \n\t  float wireframe_map(float value, float min1, float max1, float min2, float max2) {\n\t\t  return min2 + (value - min1) * (max2 - min2) / (max1 - min1);\n\t  }\n  \n\t  float getWireframe() {\n\t\t\tvec3 barycentric = v_edges_Barycentric;\n\t\t\n\t\t\t// Distance from center of each triangle to its edges.\n\t\t\tfloat d = min(min(barycentric.x, barycentric.y), barycentric.z);\n\n\t\t\t// for dashed rendering, we can use this to get the 0 .. 1 value of the line length\n\t\t\tfloat positionAlong = max(barycentric.x, barycentric.y);\n\t\t\tif (barycentric.y < barycentric.x && barycentric.y < barycentric.z) {\n\t\t\t\tpositionAlong = 1.0 - positionAlong;\n\t\t\t}\n\n\t\t\t// the thickness of the stroke\n\t\t\tfloat computedThickness = wireframe_map(thickness, 0.0, 1.0, 0.0, 0.34);\n\n\t\t\t// if we want to shrink the thickness toward the center of the line segment\n\t\t\tif (squeeze) {\n\t\t\t\tcomputedThickness *= mix(squeezeMin, squeezeMax, (1.0 - sin(positionAlong * PI)));\n\t\t\t}\n\n\t\t\t// Create dash pattern\n\t\t\tif (dash) {\n\t\t\t\t// here we offset the stroke position depending on whether it\n\t\t\t\t// should overlap or not\n\t\t\t\tfloat offset = 1.0 / dashRepeats * dashLength / 2.0;\n\t\t\t\tif (!dashInvert) {\n\t\t\t\t\toffset += 1.0 / dashRepeats / 2.0;\n\t\t\t\t}\n\n\t\t\t\t// if we should animate the dash or not\n\t\t\t\t// if (dashAnimate) {\n\t\t\t\t// \toffset += time * 0.22;\n\t\t\t\t// }\n\n\t\t\t\t// create the repeating dash pattern\n\t\t\t\tfloat pattern = fract((positionAlong + offset) * dashRepeats);\n\t\t\t\tcomputedThickness *= 1.0 - wireframe_aastep(dashLength, pattern);\n\t\t\t}\n\n\t\t\t// compute the anti-aliased stroke edge  \n\t\t\tfloat edge = 1.0 - wireframe_aastep(computedThickness, d);\n\n\t\t\treturn edge;\n\t  }\n\t  "},l=n.shaderMaterial(i.uniforms,i.vertex+"\n  \tvoid main() {\n\t\tinitWireframe();\n\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n\t}\n  ",i.fragment+"\n  void main () {\n\t\t// Compute color\n\n\t\tfloat edge = getWireframe();\n\t\tvec4 colorStroke = vec4(stroke, edge);\n\n\t\t#ifdef FLIP_SIDED\n\t\t\tcolorStroke.rgb = backfaceStroke;\n\t\t#endif\n    \n\t\tvec4 colorFill = vec4(fill, fillOpacity);\n\t\tvec4 outColor = mix(colorFill, colorStroke, edge * strokeOpacity);\n\n\t\tgl_FragColor = outColor;\n\t}\n  ");exports.WireframeMaterial=l,exports.WireframeMaterialShaders=i,exports.setWireframeOverride=function(t,e){t.onBeforeCompile=t=>{t.uniforms={...t.uniforms,...e},t.vertexShader=t.vertexShader.replace("void main() {",`\n\t\t  ${i.vertex}\n\t\t  void main() {\n\t\t\tinitWireframe();\n\t\t`),t.fragmentShader=t.fragmentShader.replace("void main() {",`\n\t\t  ${i.fragment}\n\t\t  void main() {\n\t\t`),t.fragmentShader=t.fragmentShader.replace("#include <color_fragment>","\n\t\t  #include <color_fragment>\n\t\t\t  float edge = getWireframe();\n\t\t  vec4 colorStroke = vec4(stroke, edge);\n\t\t  #ifdef FLIP_SIDED\n\t\t\tcolorStroke.rgb = backfaceStroke;\n\t\t  #endif\n\t\t  vec4 colorFill = vec4(mix(diffuseColor.rgb, fill, fillMix), mix(diffuseColor.a, fillOpacity, fillMix));\n\t\t  vec4 outColor = mix(colorFill, colorStroke, edge * strokeOpacity);\n\n\t\t  diffuseColor.rgb = outColor.rgb;\n\t\t  diffuseColor.a *= outColor.a;\n\t\t")},t.side=r.DoubleSide,t.transparent=!0},exports.useWireframeUniforms=function(t,e){o.useEffect((()=>{var n;t.fillOpacity.value=null!==(n=e.fillOpacity)&&void 0!==n?n:t.fillOpacity.value}),[e.fillOpacity]),o.useEffect((()=>{var n;t.fillMix.value=null!==(n=e.fillMix)&&void 0!==n?n:t.fillMix.value}),[e.fillMix]),o.useEffect((()=>{var n;t.strokeOpacity.value=null!==(n=e.strokeOpacity)&&void 0!==n?n:t.strokeOpacity.value}),[e.strokeOpacity]),o.useEffect((()=>{var n;t.thickness.value=null!==(n=e.thickness)&&void 0!==n?n:t.thickness.value}),[e.thickness]),o.useEffect((()=>{t.colorBackfaces.value=!!e.colorBackfaces}),[e.colorBackfaces]),o.useEffect((()=>{t.dash.value=!!e.dash}),[e.dash]),o.useEffect((()=>{t.dashInvert.value=!!e.dashInvert}),[e.dashInvert]),o.useEffect((()=>{var n;t.dashRepeats.value=null!==(n=e.dashRepeats)&&void 0!==n?n:t.dashRepeats.value}),[e.dashRepeats]),o.useEffect((()=>{var n;t.dashLength.value=null!==(n=e.dashLength)&&void 0!==n?n:t.dashLength.value}),[e.dashLength]),o.useEffect((()=>{t.squeeze.value=!!e.squeeze}),[e.squeeze]),o.useEffect((()=>{var n;t.squeezeMin.value=null!==(n=e.squeezeMin)&&void 0!==n?n:t.squeezeMin.value}),[e.squeezeMin]),o.useEffect((()=>{var n;t.squeezeMax.value=null!==(n=e.squeezeMax)&&void 0!==n?n:t.squeezeMax.value}),[e.squeezeMax]),o.useEffect((()=>{t.stroke.value=e.stroke?new r.Color(e.stroke):t.stroke.value}),[e.stroke]),o.useEffect((()=>{t.fill.value=e.fill?new r.Color(e.fill):t.fill.value}),[e.fill]),o.useEffect((()=>{t.backfaceStroke.value=e.backfaceStroke?new r.Color(e.backfaceStroke):t.backfaceStroke.value}),[e.backfaceStroke])};
