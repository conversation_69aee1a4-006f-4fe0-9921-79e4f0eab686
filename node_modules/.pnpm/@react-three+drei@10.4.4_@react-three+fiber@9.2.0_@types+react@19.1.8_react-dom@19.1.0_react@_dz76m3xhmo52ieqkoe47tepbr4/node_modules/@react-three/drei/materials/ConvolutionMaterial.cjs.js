"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("three"),n=require("../helpers/constants.cjs.js");function r(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var t=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,t.get?t:{enumerable:!0,get:function(){return e[r]}})}})),n.default=e,Object.freeze(n)}var t=r(e);class i extends t.ShaderMaterial{constructor(e=new t.Vector2){super({uniforms:{inputBuffer:new t.Uniform(null),depthBuffer:new t.Uniform(null),resolution:new t.Uniform(new t.Vector2),texelSize:new t.Uniform(new t.Vector2),halfTexelSize:new t.Uniform(new t.Vector2),kernel:new t.Uniform(0),scale:new t.Uniform(1),cameraNear:new t.Uniform(0),cameraFar:new t.Uniform(1),minDepthThreshold:new t.Uniform(0),maxDepthThreshold:new t.Uniform(1),depthScale:new t.Uniform(0),depthToBlurRatioBias:new t.Uniform(.25)},fragmentShader:`#include <common>\n        #include <dithering_pars_fragment>      \n        uniform sampler2D inputBuffer;\n        uniform sampler2D depthBuffer;\n        uniform float cameraNear;\n        uniform float cameraFar;\n        uniform float minDepthThreshold;\n        uniform float maxDepthThreshold;\n        uniform float depthScale;\n        uniform float depthToBlurRatioBias;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          float depthFactor = 0.0;\n          \n          #ifdef USE_DEPTH\n            vec4 depth = texture2D(depthBuffer, vUv);\n            depthFactor = smoothstep(minDepthThreshold, maxDepthThreshold, 1.0-(depth.r * depth.a));\n            depthFactor *= depthScale;\n            depthFactor = max(0.0, min(1.0, depthFactor + 0.25));\n          #endif\n          \n          vec4 sum = texture2D(inputBuffer, mix(vUv0, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv1, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv2, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv3, vUv, depthFactor));\n          gl_FragColor = sum * 0.25 ;\n\n          #include <dithering_fragment>\n          #include <tonemapping_fragment>\n          #include <${n.version>=154?"colorspace_fragment":"encodings_fragment"}>\n        }`,vertexShader:"uniform vec2 texelSize;\n        uniform vec2 halfTexelSize;\n        uniform float kernel;\n        uniform float scale;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          vec2 uv = position.xy * 0.5 + 0.5;\n          vUv = uv;\n\n          vec2 dUv = (texelSize * vec2(kernel) + halfTexelSize) * scale;\n          vUv0 = vec2(uv.x - dUv.x, uv.y + dUv.y);\n          vUv1 = vec2(uv.x + dUv.x, uv.y + dUv.y);\n          vUv2 = vec2(uv.x + dUv.x, uv.y - dUv.y);\n          vUv3 = vec2(uv.x - dUv.x, uv.y - dUv.y);\n\n          gl_Position = vec4(position.xy, 1.0, 1.0);\n        }",blending:t.NoBlending,depthWrite:!1,depthTest:!1}),this.toneMapped=!1,this.setTexelSize(e.x,e.y),this.kernel=new Float32Array([0,1,2,2,3])}setTexelSize(e,n){this.uniforms.texelSize.value.set(e,n),this.uniforms.halfTexelSize.value.set(e,n).multiplyScalar(.5)}setResolution(e){this.uniforms.resolution.value.copy(e)}}exports.ConvolutionMaterial=i;
