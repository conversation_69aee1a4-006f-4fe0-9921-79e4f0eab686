"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("react-dom/client"),n=require("@react-three/fiber"),o=require("maath");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function l(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=s(e),c=l(t),a=l(r);const f=c.createContext(null);function u(){return c.useContext(f)}const d=c.forwardRef((({children:e},t)=>{const r=c.useRef(null);c.useImperativeHandle(t,(()=>r.current),[]);const o=u(),{width:s,height:l}=n.useThree((e=>e.viewport));return n.useFrame((()=>{r.current.position.x=o.horizontal?-s*(o.pages-1)*o.offset:0,r.current.position.y=o.horizontal?0:l*(o.pages-1)*o.offset})),c.createElement("group",{ref:r},e)})),p=c.forwardRef((({children:e,style:t,...r},o)=>{const s=u(),l=c.useRef(null);c.useImperativeHandle(o,(()=>l.current),[]);const{width:d,height:p}=n.useThree((e=>e.size)),h=c.useContext(n.context),m=c.useMemo((()=>a.createRoot(s.fixed)),[s.fixed]);return n.useFrame((()=>{s.delta>s.eps&&(l.current.style.transform=`translate3d(${s.horizontal?-d*(s.pages-1)*s.offset:0}px,${s.horizontal?0:p*(s.pages-1)*-s.offset}px,0)`)})),m.render(c.createElement("div",i.default({ref:l,style:{...t,position:"absolute",top:0,left:0,willChange:"transform"}},r),c.createElement(f.Provider,{value:s},c.createElement(n.context.Provider,{value:h},e)))),null})),h=c.forwardRef((({html:e,...t},r)=>{const n=e?p:d;return c.createElement(n,i.default({ref:r},t))}));exports.Scroll=h,exports.ScrollControls=function({eps:e=1e-5,enabled:t=!0,infinite:r,horizontal:s,pages:l=1,distance:i=1,damping:a=.25,maxSpeed:u=1/0,prepend:d=!1,style:p={},children:h}){const{get:m,setEvents:v,gl:g,size:y,invalidate:x,events:w}=n.useThree(),[E]=c.useState((()=>document.createElement("div"))),[b]=c.useState((()=>document.createElement("div"))),[z]=c.useState((()=>document.createElement("div"))),C=g.domElement.parentNode,L=c.useRef(0),R=c.useMemo((()=>{const t={el:E,eps:e,fill:b,fixed:z,horizontal:s,damping:a,offset:0,delta:0,scroll:L,pages:l,range(e,t,r=0){const n=e-r,o=n+t+2*r;return this.offset<n?0:this.offset>o?1:(this.offset-n)/(o-n)},curve(e,t,r=0){return Math.sin(this.range(e,t,r)*Math.PI)},visible(e,t,r=0){const n=e-r,o=n+t+2*r;return this.offset>=n&&this.offset<=o}};return t}),[e,a,s,l]);c.useEffect((()=>{E.style.position="absolute",E.style.width="100%",E.style.height="100%",E.style[s?"overflowX":"overflowY"]="auto",E.style[s?"overflowY":"overflowX"]="hidden",E.style.top="0px",E.style.left="0px";for(const e in p)E.style[e]=p[e];z.style.position="sticky",z.style.top="0px",z.style.left="0px",z.style.width="100%",z.style.height="100%",z.style.overflow="hidden",E.appendChild(z),b.style.height=s?"100%":l*i*100+"%",b.style.width=s?l*i*100+"%":"100%",b.style.pointerEvents="none",E.appendChild(b),d?C.prepend(E):C.appendChild(E),E[s?"scrollLeft":"scrollTop"]=1;const e=w.connected||g.domElement;requestAnimationFrame((()=>null==w.connect?void 0:w.connect(E)));const t=m().events.compute;return v({compute(e,t){const{left:r,top:n}=C.getBoundingClientRect(),o=e.clientX-r,s=e.clientY-n;t.pointer.set(o/t.size.width*2-1,-s/t.size.height*2+1),t.raycaster.setFromCamera(t.pointer,t.camera)}}),()=>{C.removeChild(E),v({compute:t}),null==w.connect||w.connect(e)}}),[l,i,s,E,b,z,C]),c.useEffect((()=>{if(w.connected===E){const e=y[s?"width":"height"],n=E[s?"scrollWidth":"scrollHeight"],o=n-e;let l=0,i=!0,c=!0;const a=()=>{if(t&&!c&&(x(),l=E[s?"scrollLeft":"scrollTop"],L.current=l/o,r)){if(!i)if(l>=o){const e=1-R.offset;E[s?"scrollLeft":"scrollTop"]=1,L.current=R.offset=-e,i=!0}else if(l<=0){const e=1+R.offset;E[s?"scrollLeft":"scrollTop"]=n,L.current=R.offset=e,i=!0}i&&setTimeout((()=>i=!1),40)}};E.addEventListener("scroll",a,{passive:!0}),requestAnimationFrame((()=>c=!1));const f=e=>E.scrollLeft+=e.deltaY/2;return s&&E.addEventListener("wheel",f,{passive:!0}),()=>{E.removeEventListener("scroll",a),s&&E.removeEventListener("wheel",f)}}}),[E,w,y,r,R,x,s,t]);let T=0;return n.useFrame(((t,r)=>{T=R.offset,o.easing.damp(R,"offset",L.current,a,r,u,void 0,e),o.easing.damp(R,"delta",Math.abs(T-R.offset),a,r,u,void 0,e),R.delta>e&&x()})),c.createElement(f.Provider,{value:R},h)},exports.useScroll=u;
