"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("three"),n=require("@react-three/fiber"),a=require("@use-gesture/react");function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var c=o(e),u=i(r),s=i(t);const l=new s.Vector3,m=new s.Vector2,f=new s.Vector3,d=new s.Vector3,x=new s.Vector3,p=new s.Plane,y=u.forwardRef((({autoTransform:e=!0,matrix:r,axisLock:t,dragLimits:o,onHover:i,onDragStart:y,onDrag:g,onDragEnd:h,children:b,dragConfig:v,...w},D)=>{const M=n.useThree((e=>e.controls)),{camera:j,size:z,raycaster:P,invalidate:O}=n.useThree(),V=u.useRef(null),q=a.useGesture({onHover:({hovering:e})=>i&&i(null!=e&&e),onDragStart:({event:e})=>{M&&(M.enabled=!1);const{point:r}=e;V.current.matrix.decompose(l,new s.Quaternion,new s.Vector3),f.copy(r),d.copy(f).sub(l),y&&y(l),O()},onDrag:({xy:[r,n],intentional:a})=>{if(!a)return;const i=(r-z.left)/z.width*2-1,c=-(n-z.top)/z.height*2+1;if(m.set(i,c),P.setFromCamera(m,j),t)switch(t){case"x":x.set(1,0,0);break;case"y":x.set(0,1,0);break;case"z":x.set(0,0,1)}else j.getWorldDirection(x).negate();p.setFromNormalAndCoplanarPoint(x,f),P.ray.intersectPlane(p,f);const u=V.current.matrix.clone(),l=V.current.matrixWorld.clone(),y=new s.Vector3(f.x-d.x,f.y-d.y,f.z-d.z);if(o&&(y.x=o[0]?Math.max(Math.min(y.x,o[0][1]),o[0][0]):y.x,y.y=o[1]?Math.max(Math.min(y.y,o[1][1]),o[1][0]):y.y,y.z=o[2]?Math.max(Math.min(y.z,o[2][1]),o[2][0]):y.z),e){V.current.matrix.setPosition(y);const e=V.current.matrix.clone().multiply(u.invert()),r=V.current.matrix.clone().multiply(l.invert());g&&g(V.current.matrix,e,V.current.matrixWorld,r)}else{const e=(new s.Matrix4).copy(V.current.matrix);e.setPosition(y);const r=e.clone().multiply(u.invert()),t=e.clone().multiply(l.invert());g&&g(e,r,V.current.matrixWorld,t)}O()},onDragEnd:()=>{M&&(M.enabled=!0),h&&h(),O()}},{drag:{filterTaps:!0,threshold:1,..."object"==typeof v?v:{}}});return u.useImperativeHandle(D,(()=>V.current),[]),u.useLayoutEffect((()=>{r&&(V.current.matrix=r)}),[r]),u.createElement("group",c.default({ref:V},q(),{matrix:r,matrixAutoUpdate:!1},w),b)}));exports.DragControls=y;
