"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("zustand"),r=require("zustand/middleware");function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var s=n(e);const o=s.createContext(null);exports.KeyboardControls=function({map:e,children:n,onChange:u,domElement:c}){const a=e.map((e=>e.name+e.keys)).join("-"),d=s.useMemo((()=>t.create(r.subscribeWithSelector((()=>e.reduce(((e,t)=>({...e,[t.name]:!1})),{}))))),[a]),i=s.useMemo((()=>[d.subscribe,d.getState,d]),[a]),p=d.setState;return s.useEffect((()=>{const t=e.map((({name:e,keys:t,up:r})=>({keys:t,up:r,fn:t=>{p({[e]:t}),u&&u(e,t,i[1]())}}))).reduce(((e,{keys:t,fn:r,up:n=!0})=>(t.forEach((t=>e[t]={fn:r,pressed:!1,up:n})),e)),{}),r=({key:e,code:r})=>{const n=t[e]||t[r];if(!n)return;const{fn:s,pressed:o,up:u}=n;n.pressed=!0,!u&&o||s(!0)},n=({key:e,code:r})=>{const n=t[e]||t[r];if(!n)return;const{fn:s,up:o}=n;n.pressed=!1,o&&s(!1)},s=c||window;return s.addEventListener("keydown",r,{passive:!0}),s.addEventListener("keyup",n,{passive:!0}),()=>{s.removeEventListener("keydown",r),s.removeEventListener("keyup",n)}}),[c,a]),s.createElement(o.Provider,{value:i,children:n})},exports.useKeyboardControls=function(e){const[t,r,n]=s.useContext(o);return e?n(e):[t,r]};
