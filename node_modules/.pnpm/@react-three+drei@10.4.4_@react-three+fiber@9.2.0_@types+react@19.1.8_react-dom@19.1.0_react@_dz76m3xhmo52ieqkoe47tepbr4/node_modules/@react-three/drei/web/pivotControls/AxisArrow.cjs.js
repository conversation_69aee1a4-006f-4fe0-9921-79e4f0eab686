"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("three"),r=require("@react-three/fiber"),n=require("../../core/Line.cjs.js"),o=require("../Html.cjs.js"),a=require("./context.cjs.js");function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("@babel/runtime/helpers/extends"),require("three-stdlib"),require("react-dom/client");var c=i(e),l=i(t);const s=new l.Vector3,u=new l.Vector3,d=(e,t,r,n)=>{const o=t.dot(t),a=t.dot(e)-t.dot(r),i=t.dot(n);if(0===i)return-a/o;s.copy(n).multiplyScalar(o/i).sub(t),u.copy(n).multiplyScalar(a/i).add(r).sub(e);return-s.dot(u)/s.dot(s)},p=new l.Vector3(0,1,0),m=new l.Matrix4;exports.AxisArrow=({direction:e,axis:t})=>{const{translation:i,translationLimits:s,annotations:u,annotationsClass:f,depthTest:x,scale:y,lineWidth:g,fixed:h,axisColors:b,hoveredColor:P,opacity:w,onDragStart:O,onDrag:v,onDragEnd:j,userData:E}=c.useContext(a.context),M=r.useThree((e=>e.controls)),k=c.useRef(null),C=c.useRef(null),q=c.useRef(null),L=c.useRef(0),[D,F]=c.useState(!1),R=c.useCallback((r=>{u&&(k.current.innerText=`${i.current[t].toFixed(2)}`,k.current.style.display="block"),r.stopPropagation();const n=(new l.Matrix4).extractRotation(C.current.matrixWorld),o=r.point.clone(),a=(new l.Vector3).setFromMatrixPosition(C.current.matrixWorld),c=e.clone().applyMatrix4(n).normalize();q.current={clickPoint:o,dir:c},L.current=i.current[t],O({component:"Arrow",axis:t,origin:a,directions:[c]}),M&&(M.enabled=!1),r.target.setPointerCapture(r.pointerId)}),[u,e,M,O,i,t]),T=c.useCallback((e=>{if(e.stopPropagation(),D||F(!0),q.current){const{clickPoint:r,dir:n}=q.current,[o,a]=(null==s?void 0:s[t])||[void 0,void 0];let c=d(r,n,e.ray.origin,e.ray.direction);void 0!==o&&(c=Math.max(c,o-L.current)),void 0!==a&&(c=Math.min(c,a-L.current)),i.current[t]=L.current+c,u&&(k.current.innerText=`${i.current[t].toFixed(2)}`),m.makeTranslation(n.x*c,n.y*c,n.z*c),v(m)}}),[u,v,D,i,s,t]),S=c.useCallback((e=>{u&&(k.current.style.display="none"),e.stopPropagation(),q.current=null,j(),M&&(M.enabled=!0),e.target.releasePointerCapture(e.pointerId)}),[u,M,j]),W=c.useCallback((e=>{e.stopPropagation(),F(!1)}),[]),{cylinderLength:V,coneWidth:z,coneLength:A,matrixL:_}=c.useMemo((()=>{const t=h?g/y*1.6:y/20,r=h?.2:y/5,n=h?1-r:y-r,o=(new l.Quaternion).setFromUnitVectors(p,e.clone().normalize());return{cylinderLength:n,coneWidth:t,coneLength:r,matrixL:(new l.Matrix4).makeRotationFromQuaternion(o)}}),[e,y,g,h]),U=D?P:b[t];return c.createElement("group",{ref:C},c.createElement("group",{matrix:_,matrixAutoUpdate:!1,onPointerDown:R,onPointerMove:T,onPointerUp:S,onPointerOut:W},u&&c.createElement(o.Html,{position:[0,-A,0]},c.createElement("div",{style:{display:"none",background:"#151520",color:"white",padding:"6px 8px",borderRadius:7,whiteSpace:"nowrap"},className:f,ref:k})),c.createElement("mesh",{visible:!1,position:[0,(V+A)/2,0],userData:E},c.createElement("cylinderGeometry",{args:[1.4*z,1.4*z,V+A,8,1]})),c.createElement(n.Line,{transparent:!0,raycast:()=>null,depthTest:x,points:[0,0,0,0,V,0],lineWidth:g,side:l.DoubleSide,color:U,opacity:w,polygonOffset:!0,renderOrder:1,polygonOffsetFactor:-10,fog:!1}),c.createElement("mesh",{raycast:()=>null,position:[0,V+A/2,0],renderOrder:500},c.createElement("coneGeometry",{args:[z,A,24,1]}),c.createElement("meshBasicMaterial",{transparent:!0,depthTest:x,color:U,opacity:w,polygonOffset:!0,polygonOffsetFactor:-10,fog:!1}))))},exports.calculateOffset=d;
