"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("three"),r=require("@react-three/fiber"),n=require("../Html.cjs.js"),o=require("./context.cjs.js"),a=require("../../core/calculateScaleFactor.cjs.js");function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("@babel/runtime/helpers/extends"),require("react-dom/client");var c=i(e),l=i(t);const s=new l.Vector3,u=new l.Vector3,p=(e,t,r,n)=>{const o=t.dot(t),a=t.dot(e)-t.dot(r),i=t.dot(n);if(0===i)return-a/o;s.copy(n).multiplyScalar(o/i).sub(t),u.copy(n).multiplyScalar(a/i).add(r).sub(e);return-s.dot(u)/s.dot(s)},d=new l.Vector3(0,1,0),m=new l.Vector3,f=new l.Matrix4;exports.ScalingSphere=({direction:e,axis:t})=>{const{scaleLimits:i,annotations:u,annotationsClass:x,depthTest:y,scale:g,lineWidth:h,fixed:b,axisColors:P,hoveredColor:M,opacity:w,onDragStart:v,onDrag:j,onDragEnd:O,userData:k}=c.useContext(o.context),C=r.useThree((e=>e.size)),S=r.useThree((e=>e.controls)),E=c.useRef(null),R=c.useRef(null),q=c.useRef(null),F=c.useRef(1),D=c.useRef(1),L=c.useRef(null),[T,V]=c.useState(!1),z=b?1.2:1.2*g,G=c.useCallback((r=>{u&&(E.current.innerText=`${D.current.toFixed(2)}`,E.current.style.display="block"),r.stopPropagation();const n=(new l.Matrix4).extractRotation(R.current.matrixWorld),o=r.point.clone(),i=(new l.Vector3).setFromMatrixPosition(R.current.matrixWorld),c=e.clone().applyMatrix4(n).normalize(),p=R.current.matrixWorld.clone(),d=p.clone().invert(),m=b?1/a.calculateScaleFactor(R.current.getWorldPosition(s),g,r.camera,C):1;L.current={clickPoint:o,dir:c,mPLG:p,mPLGInv:d,offsetMultiplier:m},v({component:"Sphere",axis:t,origin:i,directions:[c]}),S&&(S.enabled=!1),r.target.setPointerCapture(r.pointerId)}),[u,S,e,v,t,b,g,C]),W=c.useCallback((e=>{if(e.stopPropagation(),T||V(!0),L.current){const{clickPoint:r,dir:n,mPLG:o,mPLGInv:a,offsetMultiplier:c}=L.current,[l,s]=(null==i?void 0:i[t])||[1e-5,void 0],d=p(r,n,e.ray.origin,e.ray.direction)*c,x=b?d:d/g;let y=Math.pow(2,.2*x);e.shiftKey&&(y=Math.round(10*y)/10),y=Math.max(y,l/F.current),void 0!==s&&(y=Math.min(y,s/F.current)),D.current=F.current*y,q.current.position.set(0,z+d,0),u&&(E.current.innerText=`${D.current.toFixed(2)}`),m.set(1,1,1),m.setComponent(t,y),f.makeScale(m.x,m.y,m.z).premultiply(o).multiply(a),j(f)}}),[u,z,j,T,i,t]),I=c.useCallback((e=>{u&&(E.current.style.display="none"),e.stopPropagation(),F.current=D.current,L.current=null,q.current.position.set(0,z,0),O(),S&&(S.enabled=!0),e.target.releasePointerCapture(e.pointerId)}),[u,S,O,z]),_=c.useCallback((e=>{e.stopPropagation(),V(!1)}),[]),{radius:U,matrixL:H}=c.useMemo((()=>{const t=b?h/g*1.8:g/22.5,r=(new l.Quaternion).setFromUnitVectors(d,e.clone().normalize());return{radius:t,matrixL:(new l.Matrix4).makeRotationFromQuaternion(r)}}),[e,g,h,b]),Q=T?M:P[t];return c.createElement("group",{ref:R},c.createElement("group",{matrix:H,matrixAutoUpdate:!1,onPointerDown:G,onPointerMove:W,onPointerUp:I,onPointerOut:_},u&&c.createElement(n.Html,{position:[0,z/2,0]},c.createElement("div",{style:{display:"none",background:"#151520",color:"white",padding:"6px 8px",borderRadius:7,whiteSpace:"nowrap"},className:x,ref:E})),c.createElement("mesh",{ref:q,position:[0,z,0],renderOrder:500,userData:k},c.createElement("sphereGeometry",{args:[U,12,12]}),c.createElement("meshBasicMaterial",{transparent:!0,depthTest:y,color:Q,opacity:w,polygonOffset:!0,polygonOffsetFactor:-10}))))},exports.calculateOffset=p;
