"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),a=require("@react-three/fiber"),o=require("@use-gesture/react"),n=require("maath");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function s(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var l=u(e),i=s(t);exports.PresentationControls=function({enabled:e=!0,snap:t,global:u,domElement:s,cursor:c=!0,children:d,speed:m=1,rotation:f=[0,0,0],zoom:p=1,polar:g=[0,Math.PI/2],azimuth:h=[-1/0,1/0],damping:b=.25}){const y=a.useThree((e=>e.events)),M=a.useThree((e=>e.gl)),v=s||y.connected||M.domElement,{size:E}=a.useThree(),j=i.useMemo((()=>[f[0]+g[0],f[0]+g[1]]),[f[0],g[0],g[1]]),O=i.useMemo((()=>[f[1]+h[0],f[1]+h[1]]),[f[1],h[0],h[1]]),P=i.useMemo((()=>[r.MathUtils.clamp(f[0],...j),r.MathUtils.clamp(f[1],...O),f[2]]),[f[0],f[1],f[2],j,O]);i.useEffect((()=>{if(u&&c&&e)return v.style.cursor="grab",M.domElement.style.cursor="",()=>{v.style.cursor="default",M.domElement.style.cursor="default"}}),[u,c,v,e]);const[q]=i.useState({scale:1,rotation:P,damping:b}),z=i.useRef(null);a.useFrame(((e,t)=>{n.easing.damp3(z.current.scale,q.scale,q.damping,t),n.easing.dampE(z.current.rotation,q.rotation,q.damping,t)}));const U=o.useGesture({onHover:({last:t})=>{c&&!u&&e&&(v.style.cursor=t?"auto":"grab")},onDrag:({down:a,delta:[o,n],memo:[u,s]=q.rotation||P})=>e?(c&&(v.style.cursor=a?"grabbing":"grab"),o=r.MathUtils.clamp(s+o/E.width*Math.PI*m,...O),n=r.MathUtils.clamp(u+n/E.height*Math.PI*m,...j),q.scale=a&&n>j[1]/2?p:1,q.rotation=t&&!a?P:[n,o,0],q.damping=t&&!a&&"boolean"!=typeof t?t:b,[n,o]):[n,o]},{target:u?v:void 0});return i.createElement("group",l.default({ref:z},null==U?void 0:U()),d)};
