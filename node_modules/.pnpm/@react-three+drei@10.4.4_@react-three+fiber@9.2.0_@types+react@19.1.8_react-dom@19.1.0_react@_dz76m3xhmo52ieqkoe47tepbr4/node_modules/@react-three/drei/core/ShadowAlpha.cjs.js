"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@react-three/fiber"),r=require("react"),a=require("three");function t(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(a){if("default"!==a){var t=Object.getOwnPropertyDescriptor(e,a);Object.defineProperty(r,a,t.get?t:{enumerable:!0,get:function(){return e[a]}})}})),r.default=e,Object.freeze(r)}var n=t(r),u=t(a);exports.ShadowAlpha=function({opacity:r,alphaMap:a}){const t=n.useRef(null),l=n.useRef(null),o=n.useRef({value:1}),c=n.useRef({value:null}),i=n.useRef({value:!1});return n.useLayoutEffect((()=>{t.current.onBeforeCompile=l.current.onBeforeCompile=e=>{const r=e.fragmentShader.indexOf("void main");let a,t="",n=r;for(;"\n"!==a&&n<r+100;)a=e.fragmentShader.charAt(n),t+=a,n++;t=t.trim(),e.vertexShader=e.vertexShader.replace("void main() {","\n        varying vec2 custom_vUv;\n\n        void main() {\n          custom_vUv = uv;\n          \n        "),e.fragmentShader=e.fragmentShader.replace(t,"\n          uniform float uShadowOpacity;\n          uniform sampler2D uAlphaMap;\n          uniform bool uHasAlphaMap;\n\n          varying vec2 custom_vUv;\n  \n          float bayerDither2x2( vec2 v ) {\n            return mod( 3.0 * v.y + 2.0 * v.x, 4.0 );\n          }\n    \n          float bayerDither4x4( vec2 v ) {\n            vec2 P1 = mod( v, 2.0 );\n            vec2 P2 = mod( floor( 0.5  * v ), 2.0 );\n            return 4.0 * bayerDither2x2( P1 ) + bayerDither2x2( P2 );\n          }\n  \n          void main() {\n            float alpha = \n              uHasAlphaMap ? \n                uShadowOpacity * texture2D(uAlphaMap, custom_vUv).x\n              : uShadowOpacity;\n\n            if( ( bayerDither4x4( floor( mod( gl_FragCoord.xy, 4.0 ) ) ) ) / 16.0 >= alpha ) discard;\n            \n          "),e.uniforms.uShadowOpacity=o.current,e.uniforms.uAlphaMap=c.current,e.uniforms.uHasAlphaMap=i.current}}),[]),e.useFrame((()=>{var e;const n=null==(e=t.current.__r3f)||null==(e=e.parent)?void 0:e.object;if(n){const e=n.material;e&&(o.current.value=null!=r?r:e.opacity,!1===a?(c.current.value=null,i.current.value=!1):(c.current.value=a||e.alphaMap,i.current.value=!!c.current.value))}})),n.createElement(n.Fragment,null,n.createElement("meshDepthMaterial",{ref:t,attach:"customDepthMaterial",depthPacking:u.RGBADepthPacking}),n.createElement("meshDistanceMaterial",{ref:l,attach:"customDistanceMaterial"}))};
