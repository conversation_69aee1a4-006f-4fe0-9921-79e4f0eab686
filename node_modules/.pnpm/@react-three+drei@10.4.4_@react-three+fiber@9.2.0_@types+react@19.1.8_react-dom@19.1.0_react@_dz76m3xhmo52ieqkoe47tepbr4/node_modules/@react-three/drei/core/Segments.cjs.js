"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("three"),r=require("react"),n=require("@react-three/fiber"),o=require("three-stdlib");function c(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function s(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=c(e),u=s(t),a=s(r);const l=a.createContext(null),f=a.forwardRef(((e,t)=>{a.useMemo((()=>n.extend({SegmentObject:m})),[]);const{limit:r=1e3,lineWidth:c=1,children:s,...f}=e,[d,b]=a.useState([]),[p]=a.useState((()=>new o.Line2)),[h]=a.useState((()=>new o.LineMaterial)),[g]=a.useState((()=>new o.LineSegmentsGeometry)),[v]=a.useState((()=>new u.Vector2(512,512))),[w]=a.useState((()=>Array(6*r).fill(0))),[S]=a.useState((()=>Array(6*r).fill(0))),j=a.useMemo((()=>({subscribe:e=>(b((t=>[...t,e])),()=>b((t=>t.filter((t=>t.current!==e.current)))))})),[]);return n.useFrame((()=>{for(let t=0;t<r;t++){var e;const r=null==(e=d[t])?void 0:e.current;r&&(w[6*t+0]=r.start.x,w[6*t+1]=r.start.y,w[6*t+2]=r.start.z,w[6*t+3]=r.end.x,w[6*t+4]=r.end.y,w[6*t+5]=r.end.z,S[6*t+0]=r.color.r,S[6*t+1]=r.color.g,S[6*t+2]=r.color.b,S[6*t+3]=r.color.r,S[6*t+4]=r.color.g,S[6*t+5]=r.color.b)}g.setColors(S),g.setPositions(w),p.computeLineDistances()})),a.createElement("primitive",{object:p,ref:t},a.createElement("primitive",{object:g,attach:"geometry"}),a.createElement("primitive",i.default({object:h,attach:"material",vertexColors:!0,resolution:v,linewidth:c},f)),a.createElement(l.Provider,{value:j},s))}));class m{constructor(){this.color=new u.Color("white"),this.start=new u.Vector3(0,0,0),this.end=new u.Vector3(0,0,0)}}const d=e=>e instanceof u.Vector3?e:new u.Vector3(..."number"==typeof e?[e,e,e]:e),b=a.forwardRef((({color:e,start:t,end:r},n)=>{const o=a.useContext(l);if(!o)throw"Segment must used inside Segments component.";const c=a.useRef(null);return a.useImperativeHandle(n,(()=>c.current),[]),a.useLayoutEffect((()=>o.subscribe(c)),[]),a.createElement("segmentObject",{ref:c,color:e,start:d(t),end:d(r)})}));exports.Segment=b,exports.SegmentObject=m,exports.Segments=f;
