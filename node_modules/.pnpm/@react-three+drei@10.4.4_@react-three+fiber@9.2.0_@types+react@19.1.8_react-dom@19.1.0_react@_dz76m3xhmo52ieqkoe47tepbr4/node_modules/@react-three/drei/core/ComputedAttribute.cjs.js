"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var u=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,u.get?u:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var c=u(e),a=n(t);exports.ComputedAttribute=({compute:e,name:t,...u})=>{const[n]=a.useState((()=>new r.BufferAttribute(new Float32Array(0),1))),o=a.useRef(null);return a.useLayoutEffect((()=>{if(o.current){var t;const r=null!==(t=o.current.parent)&&void 0!==t?t:o.current.__r3f.parent.object,u=e(r);o.current.copy(u)}}),[e]),a.createElement("primitive",c.default({ref:o,object:n,attach:`attributes-${t}`},u))};
