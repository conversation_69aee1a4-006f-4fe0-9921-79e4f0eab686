"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("./Text3D.cjs.js"),t=require("./Center.cjs.js");function a(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var a=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,a.get?a:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}require("@babel/runtime/helpers/extends"),require("@react-three/fiber"),require("three-stdlib"),require("./useFont.cjs.js"),require("suspend-react"),require("three");var c=a(e);const n=c.forwardRef((({font:e,color:a="#cbcbcb",bevelSize:n=.04,debug:l=!1,children:u,...s},i)=>{const[o,b]=c.useState(0),f=c.useCallback(((e=1)=>b(o+e)),[o]),d=c.useCallback(((e=1)=>b(o-e)),[o]),m=c.useMemo((()=>({incr:f,decr:d})),[f,d]);return c.useImperativeHandle(i,(()=>m),[m]),c.createElement("group",s,c.createElement(c.Suspense,{fallback:null},c.createElement(t.Center,{top:!0,cacheKey:JSON.stringify({counter:o,font:e})},c.createElement(r.Text3D,{bevelEnabled:!0,bevelSize:n,font:e},l?c.createElement("meshNormalMaterial",{wireframe:!0}):c.createElement("meshStandardMaterial",{color:a}),o))),u)}));exports.Example=n;
