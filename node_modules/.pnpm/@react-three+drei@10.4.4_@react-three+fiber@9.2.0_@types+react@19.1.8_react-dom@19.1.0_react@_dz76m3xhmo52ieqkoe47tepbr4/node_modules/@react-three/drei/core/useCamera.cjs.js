"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("three"),t=require("@react-three/fiber");function n(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var c=n(e);exports.useCamera=function(e,n){const u=t.useThree((e=>e.pointer)),[a]=c.useState((()=>{const c=new r.Raycaster;return n&&t.applyProps(c,n),function(t,n){c.setFromCamera(u,e instanceof r.Camera?e:e.current);const a=this.constructor.prototype.raycast.bind(this);a&&a(c,n)}}));return a};
