"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("three"),t=require("react"),a=require("@react-three/fiber"),n=require("./RenderCubeTexture.cjs.js");function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function o(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var a=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,a.get?a:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var c=i(e),u=o(r),s=o(t);function l({api:e}){const r=new u.Vector3,t=new u.Quaternion,n=new u.Vector3,i=new u.Euler(0,Math.PI,0);return a.useFrame((a=>{a.camera.matrixWorld.decompose(r,t,n),e.current.camera.position.copy(r),e.current.camera.quaternion.setFromEuler(i).premultiply(t)})),null}exports.Fisheye=function({renderPriority:e=1,zoom:r=0,segments:t=64,children:i,resolution:o=896,...m}){const f=s.useRef(null),p=s.useRef(null),{width:d,height:h}=a.useThree((e=>e.size)),[y]=s.useState((()=>new u.OrthographicCamera));s.useLayoutEffect((()=>{y.position.set(0,0,100),y.zoom=100,y.left=d/-2,y.right=d/2,y.top=h/2,y.bottom=h/-2,y.updateProjectionMatrix()}),[d,h]);const b=Math.sqrt(d*d+h*h)/100*(.5+r/2),g=new u.Vector3,w=new u.Sphere(new u.Vector3,b),x=new u.Matrix3,j=s.useCallback(((e,r,t)=>{r.pointer.set(e.offsetX/r.size.width*2-1,-e.offsetY/r.size.height*2+1),r.raycaster.setFromCamera(r.pointer,y),r.raycaster.ray.intersectSphere(w,g)&&(g.normalize(),x.getNormalMatrix(p.current.camera.matrixWorld),p.current.camera.getWorldPosition(r.raycaster.ray.origin),r.raycaster.ray.direction.set(0,0,1).reflect(g),r.raycaster.ray.direction.x*=-1,r.raycaster.ray.direction.applyNormalMatrix(x).multiplyScalar(-1))}),[]);return a.useFrame((r=>{e&&r.gl.render(f.current,y)}),e),s.createElement(s.Fragment,null,s.createElement("mesh",c.default({ref:f},m,{scale:b}),s.createElement("sphereGeometry",{args:[1,t,t]}),s.createElement("meshBasicMaterial",null,s.createElement(n.RenderCubeTexture,{compute:j,attach:"envMap",flip:!0,resolution:o,ref:p},i,s.createElement(l,{api:p})))))};
