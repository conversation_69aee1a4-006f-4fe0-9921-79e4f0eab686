"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("three"),t=require("@react-three/fiber");function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var n=u(e);function a(e,{path:u}){const[n]=t.useLoader(r.CubeTextureLoader,[e],(e=>e.setPath(u)));return n}a.preload=(e,{path:u})=>t.useLoader.preload(r.CubeTextureLoader,[e],(e=>e.setPath(u))),exports.CubeTexture=function({children:e,files:r,...t}){const u=a(r,{...t});return n.createElement(n.Fragment,null,null==e?void 0:e(u))},exports.useCubeTexture=a;
