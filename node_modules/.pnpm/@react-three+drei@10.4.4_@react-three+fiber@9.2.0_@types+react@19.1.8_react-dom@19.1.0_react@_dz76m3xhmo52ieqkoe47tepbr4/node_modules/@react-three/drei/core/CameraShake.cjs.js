"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("@react-three/fiber"),t=require("three-stdlib");function n(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var c=n(e);const u=c.forwardRef((({intensity:e=1,decay:n,decayRate:u=.65,maxYaw:a=.1,maxPitch:o=.1,maxRoll:s=.1,yawFrequency:i=.1,pitchFrequency:l=.1,rollFrequency:f=.1},d)=>{const m=r.useThree((e=>e.camera)),p=r.useThree((e=>e.controls)),y=c.useRef(e),h=c.useRef(m.rotation.clone()),[b]=c.useState((()=>new t.SimplexNoise)),[v]=c.useState((()=>new t.SimplexNoise)),[x]=c.useState((()=>new t.SimplexNoise)),w=()=>{(y.current<0||y.current>1)&&(y.current=y.current<0?0:1)};return c.useImperativeHandle(d,(()=>({getIntensity:()=>y.current,setIntensity:e=>{y.current=e,w()}})),[]),c.useEffect((()=>{if(p){const e=()=>{h.current=m.rotation.clone()};return p.addEventListener("change",e),e(),()=>{p.removeEventListener("change",e)}}}),[m,p]),r.useFrame(((e,r)=>{const t=Math.pow(y.current,2),c=a*t*b.noise(e.clock.elapsedTime*i,1),d=o*t*v.noise(e.clock.elapsedTime*l,1),p=s*t*x.noise(e.clock.elapsedTime*f,1);m.rotation.set(h.current.x+d,h.current.y+c,h.current.z+p),n&&y.current>0&&(y.current-=u*r,w())})),null}));exports.CameraShake=u;
