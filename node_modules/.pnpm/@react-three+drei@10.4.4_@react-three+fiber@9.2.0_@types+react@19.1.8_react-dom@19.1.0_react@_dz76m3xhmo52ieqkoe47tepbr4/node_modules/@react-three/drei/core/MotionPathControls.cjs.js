"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("three"),r=require("react"),n=require("@react-three/fiber"),o=require("maath");function c(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var s=c(e),a=u(t),i=u(r);const f=i.createContext(null);function l(){const e=i.useContext(f);if(!e)throw new Error("useMotion hook must be used in a MotionPathControls component.");return e}function m({points:e=50,color:t="black"}){const{path:r}=l(),[n,o]=i.useState([]),c=i.useMemo((()=>new a.MeshBasicMaterial({color:t})),[t]),u=i.useMemo((()=>new a.SphereGeometry(.025,16,16)),[]),s=i.useRef([]);return i.useEffect((()=>{r.curves!==s.current&&(o(r.getPoints(e)),s.current=r.curves)})),n.map(((e,t)=>i.createElement("mesh",{key:t,material:c,geometry:u,position:[e.x,e.y,e.z]})))}const p=i.forwardRef((({children:e,curves:t=[],debug:r=!1,debugColor:c="black",object:u,focus:l,loop:p=!0,offset:d,smooth:g=!1,eps:b=1e-5,damping:h=.1,focusDamping:v=.1,maxSpeed:j=1/0,...y},M)=>{const{camera:w}=n.useThree(),P=i.useRef(null),O=i.useRef(null!=d?d:0),x=i.useMemo((()=>new a.CurvePath),[]),C=i.useMemo((()=>({focus:l,object:(null==u?void 0:u.current)instanceof a.Object3D?u:{current:w},path:x,current:O.current,offset:O.current,point:new a.Vector3,tangent:new a.Vector3,next:new a.Vector3})),[l,u]),E=n.useInstanceHandle(P);i.useLayoutEffect((()=>{const e=E.current;x.curves=[];const r=t.length>0?t:e.children.map((e=>e.object));for(let e=0;e<r.length;e++)x.add(r[e]);if(g){const e=x.getPoints("number"==typeof g?g:1),t=new a.CatmullRomCurve3(e);x.curves=[t]}x.updateArcLengths()})),i.useImperativeHandle(M,(()=>Object.assign(P.current,{motion:C})),[C]),i.useLayoutEffect((()=>{O.current=o.misc.repeat(O.current,1)}),[d]);const k=i.useMemo((()=>new a.Vector3),[]);return n.useFrame(((e,t)=>{const r=C.offset;if(o.easing.damp(O,"current",void 0!==d?d:C.current,h,t,j,void 0,b),C.offset=p?o.misc.repeat(O.current,1):o.misc.clamp(O.current,0,1),x.getCurveLengths().length>0){x.getPointAt(C.offset,C.point),x.getTangentAt(C.offset,C.tangent).normalize(),x.getPointAt(o.misc.repeat(O.current-(r-C.offset),1),C.next);const e=(null==u?void 0:u.current)instanceof a.Object3D?u.current:w;e.position.copy(C.point),l&&o.easing.dampLookAt(e,(e=>(null==e?void 0:e.current)instanceof a.Object3D)(l)?l.current.getWorldPosition(k):l,v,t,j,void 0,b)}})),i.createElement("group",s.default({ref:P},y),i.createElement(f.Provider,{value:C},e,r&&i.createElement(m,{color:c})))}));exports.MotionPathControls=p,exports.useMotion=l;
