"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),n=require("@react-three/fiber"),a=require("./Texture.cjs.js"),o=require("../helpers/deprecated.cjs.js");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function c(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var l=u(e),i=c(t);const s=new r.Matrix4,d=new r.Vector3,f=new r.Quaternion,m=new r.Vector3,p=new r.Quaternion,g=new r.Vector3,h=i.createContext(null),y=i.forwardRef((({children:e,material:t=r.MeshLambertMaterial,texture:u="https://rawcdn.githack.com/pmndrs/drei-assets/9225a9f1fbd449d9411125c2f419b843d0308c9f/cloud.png",range:c,limit:y=200,frustumCulled:x,...v},b)=>{var M,w;const C=i.useMemo((()=>class extends t{constructor(){super();const e=parseInt(r.REVISION.replace(/\D+/g,""))>=154?"opaque_fragment":"output_fragment";this.onBeforeCompile=t=>{t.vertexShader="attribute float cloudOpacity;\n               varying float vOpacity;\n              "+t.vertexShader.replace("#include <fog_vertex>","#include <fog_vertex>\n                 vOpacity = cloudOpacity;\n                "),t.fragmentShader="varying float vOpacity;\n              "+t.fragmentShader.replace(`#include <${e}>`,`#include <${e}>\n                 gl_FragColor = vec4(outgoingLight, diffuseColor.a * vOpacity);\n                `)}}}),[t]);n.extend({CloudMaterial:C});const E=i.useRef(null),O=i.useRef([]),A=i.useMemo((()=>new Float32Array(Array.from({length:y},(()=>1)))),[y]),j=i.useMemo((()=>new Float32Array(Array.from({length:y},(()=>[1,1,1])).flat())),[y]),R=a.useTexture(u);let U,V=0,_=0;const q=new r.Quaternion,F=new r.Vector3(0,0,1),I=new r.Vector3;n.useFrame(((e,t)=>{for(V=e.clock.elapsedTime,s.copy(E.current.matrixWorld).invert(),e.camera.matrixWorld.decompose(m,p,g),_=0;_<O.current.length;_++)U=O.current[_],U.ref.current.matrixWorld.decompose(d,f,g),d.add(I.copy(U.position).applyQuaternion(f).multiply(g)),f.copy(p).multiply(q.setFromAxisAngle(F,U.rotation+=t*U.rotationFactor)),g.multiplyScalar(U.volume+(1+Math.sin(V*U.density*U.speed))/2*U.growth),U.matrix.compose(d,f,g).premultiply(s),U.dist=d.distanceTo(m);for(O.current.sort(((e,t)=>t.dist-e.dist)),_=0;_<O.current.length;_++)U=O.current[_],A[_]=U.opacity*(U.dist<U.fade-1?U.dist/U.fade:1),E.current.setMatrixAt(_,U.matrix),E.current.setColorAt(_,U.color);E.current.geometry.attributes.cloudOpacity.needsUpdate=!0,E.current.instanceMatrix.needsUpdate=!0,E.current.instanceColor&&(E.current.instanceColor.needsUpdate=!0)})),i.useLayoutEffect((()=>{const e=Math.min(y,void 0!==c?c:y,O.current.length);E.current.count=e,o.setUpdateRange(E.current.instanceMatrix,{start:0,count:16*e}),E.current.instanceColor&&o.setUpdateRange(E.current.instanceColor,{start:0,count:3*e}),o.setUpdateRange(E.current.geometry.attributes.cloudOpacity,{start:0,count:e})}));let D=[null!==(M=R.image.width)&&void 0!==M?M:1,null!==(w=R.image.height)&&void 0!==w?w:1];const P=Math.max(D[0],D[1]);return D=[D[0]/P,D[1]/P],i.createElement("group",l.default({ref:b},v),i.createElement(h.Provider,{value:O},e,i.createElement("instancedMesh",{matrixAutoUpdate:!1,ref:E,args:[null,null,y],frustumCulled:x},i.createElement("instancedBufferAttribute",{usage:r.DynamicDrawUsage,attach:"instanceColor",args:[j,3]}),i.createElement("planeGeometry",{args:[...D]},i.createElement("instancedBufferAttribute",{usage:r.DynamicDrawUsage,attach:"attributes-cloudOpacity",args:[A,1]})),i.createElement("cloudMaterial",{key:t.name,map:R,transparent:!0,depthWrite:!1}))))})),x=i.forwardRef((({opacity:e=1,speed:t=0,bounds:a=[5,1,1],segments:o=20,color:u="#ffffff",fade:c=10,volume:s=6,smallestVolume:d=.25,distribute:f=null,growth:m=4,concentrate:p="inside",seed:g=Math.random(),...y},x)=>{function v(){const e=1e4*Math.sin(g++);return e-Math.floor(e)}const b=i.useContext(h),M=i.useRef(null),w=i.useId(),C=i.useMemo((()=>[...new Array(o)].map(((e,t)=>({segments:o,bounds:new r.Vector3(1,1,1),position:new r.Vector3,uuid:w,index:t,ref:M,dist:0,matrix:new r.Matrix4,color:new r.Color,rotation:t*(Math.PI/o)})))),[o,w]);return i.useLayoutEffect((()=>{C.forEach(((r,l)=>{n.applyProps(r,{volume:s,color:u,speed:t,growth:m,opacity:e,fade:c,bounds:a,density:Math.max(.5,v()),rotationFactor:Math.max(.2,.5*v())*t});const i=null==f?void 0:f(r,l);var g;(i||o>1)&&r.position.copy(r.bounds).multiply(null!==(g=null==i?void 0:i.point)&&void 0!==g?g:{x:2*v()-1,y:2*v()-1,z:2*v()-1});const h=Math.abs(r.position.x),y=Math.abs(r.position.y),x=Math.abs(r.position.z),b=Math.max(h,y,x);r.length=1,h===b&&(r.length-=h/r.bounds.x),y===b&&(r.length-=y/r.bounds.y),x===b&&(r.length-=x/r.bounds.z),r.volume=(void 0!==(null==i?void 0:i.volume)?i.volume:Math.max(Math.max(0,d),"random"===p?v():"inside"===p?r.length:1-r.length))*s}))}),[p,a,c,u,e,m,s,g,o,t]),i.useLayoutEffect((()=>{const e=C;return b.current=[...b.current,...e],()=>{b.current=b.current.filter((e=>e.uuid!==w))}}),[C]),i.useImperativeHandle(x,(()=>M.current),[]),i.createElement("group",l.default({ref:M},y))})),v=i.forwardRef(((e,t)=>i.useContext(h)?i.createElement(x,l.default({ref:t},e)):i.createElement(y,null,i.createElement(x,l.default({ref:t},e)))));exports.Cloud=v,exports.CloudInstance=x,exports.Clouds=y;
