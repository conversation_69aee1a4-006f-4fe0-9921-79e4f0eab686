"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("three"),t=require("@react-three/fiber"),u=require("three-stdlib");function n(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var c=n(e),a=n(r);const i=c.forwardRef((({children:e,curve:r},n)=>{const[i]=c.useState((()=>new a.Scene)),[l,o]=c.useState(),f=c.useRef(null);return c.useLayoutEffect((()=>{f.current=new u.Flow(i.children[0]),o(f.current.object3D)}),[e]),c.useEffect((()=>{var e;r&&(null==(e=f.current)||e.updateCurve(0,r))}),[r]),c.useImperativeHandle(n,(()=>f.current)),c.createElement(c.Fragment,null,t.createPortal(e,i),l&&c.createElement("primitive",{object:l}))}));exports.CurveModifier=i;
