"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three-stdlib"),i=require("three");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function a(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var i=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,i.get?i:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var u=n(e),o=a(t);function c(e,t,r=new i.Vector3){const n=Math.PI*(e-.5),a=2*Math.PI*(t-.5);return r.x=Math.cos(a),r.y=Math.sin(n),r.z=Math.sin(a),r}const l=o.forwardRef((({inclination:e=.6,azimuth:t=.1,distance:n=1e3,mieCoefficient:a=.005,mieDirectionalG:l=.8,rayleigh:s=.5,turbidity:f=10,sunPosition:m=c(e,t),...d},b)=>{const h=o.useMemo((()=>(new i.Vector3).setScalar(n)),[n]),[y]=o.useState((()=>new r.Sky));return o.createElement("primitive",u.default({object:y,ref:b,"material-uniforms-mieCoefficient-value":a,"material-uniforms-mieDirectionalG-value":l,"material-uniforms-rayleigh-value":s,"material-uniforms-sunPosition-value":m,"material-uniforms-turbidity-value":f,scale:h},d))}));exports.Sky=l,exports.calcPosFromAngles=c;
