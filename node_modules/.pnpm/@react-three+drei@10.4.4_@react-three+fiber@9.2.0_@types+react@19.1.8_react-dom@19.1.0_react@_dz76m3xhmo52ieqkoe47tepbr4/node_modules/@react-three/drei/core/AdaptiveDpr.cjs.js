"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("@react-three/fiber");function t(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var n=t(e);exports.AdaptiveDpr=function({pixelated:e}){const t=r.useThree((e=>e.gl)),u=r.useThree((e=>e.internal.active)),i=r.useThree((e=>e.performance.current)),a=r.useThree((e=>e.viewport.initialDpr)),c=r.useThree((e=>e.setDpr));return n.useEffect((()=>{const r=t.domElement;return()=>{u&&c(a),e&&r&&(r.style.imageRendering="auto")}}),[]),n.useEffect((()=>{c(i*a),e&&t.domElement&&(t.domElement.style.imageRendering=1===i?"auto":"pixelated")}),[i]),null};
