"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("three"),t=require("@react-three/fiber"),n=require("suspend-react"),o=require("hls.js");function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var s=u(e),a=u(r);const c=((e,r)=>"undefined"!=typeof window&&"function"==typeof(null==(e=window.document)?void 0:e.createElement)&&"string"==typeof(null==(r=window.navigator)?void 0:r.userAgent))();let i=null;function l(r,{unsuspend:s="loadedmetadata",start:l=!0,hls:d={},crossOrigin:p="anonymous",muted:m=!0,loop:v=!0,playsInline:y=!0,onVideoFrame:b,...g}={}){const w=t.useThree((e=>e.gl)),h=e.useRef(null),E=n.suspend((()=>new Promise((async e=>{let t,n;"string"==typeof r?t=r:n=r;const l=Object.assign(document.createElement("video"),{src:t,srcObject:n,crossOrigin:p,loop:v,muted:m,playsInline:y,...g});if(t&&c&&t.endsWith(".m3u8")){const e=h.current=await async function(...e){var r;null!==(r=i)&&void 0!==r||(i=await Promise.resolve().then((function(){return u(require("hls.js"))})));const t=i.default;return t.isSupported()?new t(...e):null}(d);e&&(e.on(o.Events.MEDIA_ATTACHED,(()=>{e.loadSource(t)})),e.attachMedia(l))}const f=new a.VideoTexture(l);f.colorSpace=w.outputColorSpace,l.addEventListener(s,(()=>e(f)))}))),[r]),O=E.source.data;return f(O,b),e.useEffect((()=>(l&&E.image.play(),()=>{h.current&&(h.current.destroy(),h.current=null)})),[E,l]),E}const d=e.forwardRef((({children:r,src:t,...n},o)=>{const u=l(t,n);return e.useEffect((()=>()=>{u.dispose()}),[u]),e.useImperativeHandle(o,(()=>u),[u]),s.createElement(s.Fragment,null,null==r?void 0:r(u))})),f=(r,t)=>{e.useEffect((()=>{if(!t)return;if(!r.requestVideoFrameCallback)return;let e;const n=(...o)=>{t(...o),e=r.requestVideoFrameCallback(n)};return r.requestVideoFrameCallback(n),()=>r.cancelVideoFrameCallback(e)}),[r,t])};exports.VideoTexture=d,exports.useVideoTexture=l;
