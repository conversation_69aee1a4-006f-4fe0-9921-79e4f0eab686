"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("@react-three/fiber"),t=require("react"),s=require("three"),o=require("three-mesh-bvh");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var s=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,s.get?s:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var a=n(e),c=u(t);const i=e=>e.isMesh;const d=c.forwardRef((({enabled:e=!0,firstHitOnly:t=!1,children:n,strategy:u=o.SAH,verbose:d=!1,setBoundingBox:f=!0,maxDepth:p=40,maxLeafTris:y=10,indirect:l=!1,...m},B)=>{const b=c.useRef(null),T=r.useThree((e=>e.raycaster));return c.useImperativeHandle(B,(()=>b.current),[]),c.useEffect((()=>{if(e){const e={strategy:u,verbose:d,setBoundingBox:f,maxDepth:p,maxLeafTris:y,indirect:l},r=b.current;return T.firstHitOnly=t,r.traverse((r=>{i(r)&&!r.geometry.boundsTree&&r.raycast===s.Mesh.prototype.raycast&&(r.raycast=o.acceleratedRaycast,r.geometry.computeBoundsTree=o.computeBoundsTree,r.geometry.disposeBoundsTree=o.disposeBoundsTree,r.geometry.computeBoundsTree(e))})),()=>{delete T.firstHitOnly,r.traverse((e=>{i(e)&&e.geometry.boundsTree&&(e.geometry.disposeBoundsTree(),e.raycast=s.Mesh.prototype.raycast)}))}}}),[]),c.createElement("group",a.default({ref:b},m),n)}));exports.Bvh=d,exports.useBVH=function(e,r){r={strategy:o.SAH,verbose:!1,setBoundingBox:!0,maxDepth:40,maxLeafTris:10,indirect:!1,...r},c.useEffect((()=>{if(e.current){e.current.raycast=o.acceleratedRaycast;const t=e.current.geometry;return t.computeBoundsTree=o.computeBoundsTree,t.disposeBoundsTree=o.disposeBoundsTree,t.computeBoundsTree(r),()=>{t.boundsTree&&t.disposeBoundsTree()}}}),[e,JSON.stringify(r)])};
