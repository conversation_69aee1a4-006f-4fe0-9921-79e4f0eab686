"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@react-three/fiber"),t=require("three"),n=require("three-stdlib"),r=require("@monogrid/gainmap-js"),s=require("../helpers/environment-assets.cjs.js"),o=require("react");const i="https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/",p=e=>Array.isArray(e),a=["/px.png","/nx.png","/py.png","/ny.png","/pz.png","/nz.png"];function l({files:n=a,path:r="",preset:l,colorSpace:c,extensions:u}={}){l&&(d(l),n=s.presetsObj[l],r=i);const h=p(n),{extension:f,isCubemap:j}=g(n),m=b(f);if(!m)throw new Error("useEnvironment: Unrecognized file extension: "+n);const x=e.useThree((e=>e.gl));o.useLayoutEffect((()=>{"webp"!==f&&"jpg"!==f&&"jpeg"!==f||x.domElement.addEventListener("webglcontextlost",(function(){e.useLoader.clear(m,h?[n]:n)}),{once:!0})}),[n,x.domElement]);const w=e.useLoader(m,h?[n]:n,(e=>{"webp"!==f&&"jpg"!==f&&"jpeg"!==f||e.setRenderer(x),null==e.setPath||e.setPath(r),u&&u(e)}));let E=h?w[0]:w;var v;"jpg"!==f&&"jpeg"!==f&&"webp"!==f||(E=null==(v=E.renderTarget)?void 0:v.texture);return E.mapping=j?t.CubeReflectionMapping:t.EquirectangularReflectionMapping,E.colorSpace=null!=c?c:j?"srgb":"srgb-linear",E}const c={files:a,path:"",preset:void 0,extensions:void 0};l.preload=t=>{const n={...c,...t};let{files:r,path:o=""}=n;const{preset:a,extensions:l}=n;a&&(d(a),r=s.presetsObj[a],o=i);const{extension:u}=g(r);if("webp"===u||"jpg"===u||"jpeg"===u)throw new Error("useEnvironment: Preloading gainmaps is not supported");const h=b(u);if(!h)throw new Error("useEnvironment: Unrecognized file extension: "+r);e.useLoader.preload(h,p(r)?[r]:r,(e=>{null==e.setPath||e.setPath(o),l&&l(e)}))};const u={files:a,preset:void 0};function d(e){if(!(e in s.presetsObj))throw new Error("Preset must be one of: "+Object.keys(s.presetsObj).join(", "))}function g(e){var t;const n=p(e)&&6===e.length,r=p(e)&&3===e.length&&e.some((e=>e.endsWith("json"))),s=p(e)?e[0]:e;return{extension:n?"cube":r?"webp":s.startsWith("data:application/exr")?"exr":s.startsWith("data:application/hdr")?"hdr":s.startsWith("data:image/jpeg")?"jpg":null==(t=s.split(".").pop())||null==(t=t.split("?"))||null==(t=t.shift())?void 0:t.toLowerCase(),isCubemap:n,isGainmap:r}}function b(e){return"cube"===e?t.CubeTextureLoader:"hdr"===e?n.RGBELoader:"exr"===e?n.EXRLoader:"jpg"===e||"jpeg"===e?r.HDRJPGLoader:"webp"===e?r.GainMapLoader:null}l.clear=t=>{const n={...u,...t};let{files:r}=n;const{preset:o}=n;o&&(d(o),r=s.presetsObj[o]);const{extension:i}=g(r),a=b(i);if(!a)throw new Error("useEnvironment: Unrecognized file extension: "+r);e.useLoader.clear(a,p(r)?[r]:r)},exports.useEnvironment=l;
