"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("@react-three/fiber"),r=require("react"),n=require("three-stdlib");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function o(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var s=a(e),u=o(r);const c=u.forwardRef(((e={enableDamping:!0},r)=>{const{domElement:a,camera:o,makeDefault:c,onChange:i,onStart:d,onEnd:l,...f}=e,m=t.useThree((e=>e.invalidate)),v=t.useThree((e=>e.camera)),b=t.useThree((e=>e.gl)),p=t.useThree((e=>e.events)),h=t.useThree((e=>e.set)),E=t.useThree((e=>e.get)),g=a||p.connected||b.domElement,j=o||v,O=u.useMemo((()=>new n.MapControls(j)),[j]);return u.useEffect((()=>{O.connect(g);const e=e=>{m(),i&&i(e)};return O.addEventListener("change",e),d&&O.addEventListener("start",d),l&&O.addEventListener("end",l),()=>{O.dispose(),O.removeEventListener("change",e),d&&O.removeEventListener("start",d),l&&O.removeEventListener("end",l)}}),[i,d,l,O,m,g]),u.useEffect((()=>{if(c){const e=E().controls;return h({controls:O}),()=>h({controls:e})}}),[c,O]),t.useFrame((()=>O.update()),-1),u.createElement("primitive",s.default({ref:r,object:O,enableDamping:!0},f))}));exports.MapControls=c;
