"use strict";function e(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}Object.defineProperty(exports,"__esModule",{value:!0});var t=e(require("three"));const r=new t.Vector3,o=new t.Vector3,n=new t.Vector3,c=(e,t,o,n=1)=>{const c=r.set(e.x/o.width*2-1,-e.y/o.height*2+1,n);return c.unproject(t),c};exports.calculateScaleFactor=(e,t,r,u)=>{const a=((e,t,r)=>{const o=r.width/2,n=r.height/2;t.updateMatrixWorld(!1);const c=e.project(t);return c.x=c.x*o+o,c.y=-c.y*n+n,c})(n.copy(e),r,u);let s=0;for(let n=0;n<2;++n){const i=o.copy(a).setComponent(n,a.getComponent(n)+t),l=c(i,r,u,i.z);s=Math.max(s,e.distanceTo(l))}return s};
