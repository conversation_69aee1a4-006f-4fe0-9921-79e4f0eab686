"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("three"),r=require("react"),n=require("../helpers/constants.cjs.js");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function o(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=a(e),l=o(t),c=o(r);const s=n.version>=154?"opaque_fragment":"output_fragment";class u extends l.PointsMaterial{constructor(e){super(e),this.onBeforeCompile=(e,t)=>{const{isWebGL2:r}=t.capabilities;e.fragmentShader=e.fragmentShader.replace(`#include <${s}>`,`\n        ${r?`#include <${s}>`:`#extension GL_OES_standard_derivatives : enable\n#include <${s}>`}\n      vec2 cxy = 2.0 * gl_PointCoord - 1.0;\n      float r = dot(cxy, cxy);\n      float delta = fwidth(r);     \n      float mask = 1.0 - smoothstep(1.0 - delta, 1.0 + delta, r);\n      gl_FragColor = vec4(gl_FragColor.rgb, mask * gl_FragColor.a );\n      #include <tonemapping_fragment>\n      #include <${n.version>=154?"colorspace_fragment":"encodings_fragment"}>\n      `)}}}const f=c.forwardRef(((e,t)=>{const[r]=c.useState((()=>new u(null)));return c.createElement("primitive",i.default({},e,{object:r,ref:t,attach:"material"}))}));exports.PointMaterial=f,exports.PointMaterialImpl=u;
