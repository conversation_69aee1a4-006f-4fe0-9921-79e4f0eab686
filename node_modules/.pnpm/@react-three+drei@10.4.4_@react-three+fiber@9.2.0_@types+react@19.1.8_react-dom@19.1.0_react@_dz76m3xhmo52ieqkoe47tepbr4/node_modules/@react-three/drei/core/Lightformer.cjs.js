"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("@react-three/fiber"),t=require("react"),a=require("three");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function o(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var a=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,a.get?a:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var c=n(e),l=o(t),i=o(a);const u=l.forwardRef((({light:e,args:t,map:a,toneMapped:n=!1,color:o="white",form:u="rect",intensity:s=1,scale:f=1,target:m=[0,0,0],children:p,...d},y)=>{const g=l.useRef(null);return l.useImperativeHandle(y,(()=>g.current),[]),l.useLayoutEffect((()=>{p||d.material||(r.applyProps(g.current.material,{color:o}),g.current.material.color.multiplyScalar(s))}),[o,s,p,d.material]),l.useLayoutEffect((()=>{d.rotation||g.current.quaternion.identity(),m&&!d.rotation&&("boolean"==typeof m?g.current.lookAt(0,0,0):g.current.lookAt(Array.isArray(m)?new i.Vector3(...m):m))}),[m,d.rotation]),f=Array.isArray(f)&&2===f.length?[f[0],f[1],1]:f,l.createElement("mesh",c.default({ref:g,scale:f},d),"circle"===u?l.createElement("ringGeometry",{args:t||[0,.5,64]}):"ring"===u?l.createElement("ringGeometry",{args:t||[.25,.5,64]}):"rect"===u||"plane"===u?l.createElement("planeGeometry",{args:t||[1,1]}):"box"===u?l.createElement("boxGeometry",{args:t||[1,1,1]}):l.createElement(u,{args:t}),p||l.createElement("meshBasicMaterial",{toneMapped:n,map:a,side:i.DoubleSide}),e&&l.createElement("pointLight",c.default({castShadow:!0},e)))}));exports.Lightformer=u;
