"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("three"),n=require("./Line.cjs.js");function c(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}require("@react-three/fiber"),require("three-stdlib");var o=c(e),u=i(r);const s=u.forwardRef((function({start:e,end:r,midA:c,midB:i,segments:s=20,...f},a){const d=u.useMemo((()=>{const n=e instanceof t.Vector3?e:new t.Vector3(...e),o=r instanceof t.Vector3?r:new t.Vector3(...r),u=c instanceof t.Vector3?c:new t.Vector3(...c),f=i instanceof t.Vector3?i:new t.Vector3(...i);return new t.CubicBezierCurve3(n,u,f,o).getPoints(s)}),[e,r,c,i,s]);return u.createElement(n.Line,o.default({ref:a,points:d},f))}));exports.CubicBezierLine=s;
