"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("@react-three/fiber"),r=require("three");function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var a=n(e),o=n(r);const i=a.forwardRef((({children:e,enabled:r=!0,speed:n=1,rotationIntensity:i=1,floatIntensity:u=1,floatingRange:c=[-.1,.1],autoInvalidate:l=!1,...s},d)=>{const f=a.useRef(null);a.useImperativeHandle(d,(()=>f.current),[]);const p=a.useRef(1e4*Math.random());return t.useFrame((e=>{var t,a;if(!r||0===n)return;l&&e.invalidate();const s=p.current+e.clock.elapsedTime;f.current.rotation.x=Math.cos(s/4*n)/8*i,f.current.rotation.y=Math.sin(s/4*n)/8*i,f.current.rotation.z=Math.sin(s/4*n)/20*i;let d=Math.sin(s/4*n)/10;d=o.MathUtils.mapLinear(d,-.1,.1,null!==(t=null==c?void 0:c[0])&&void 0!==t?t:-.1,null!==(a=null==c?void 0:c[1])&&void 0!==a?a:.1),f.current.position.y=d*u,f.current.updateMatrix()})),a.createElement("group",s,a.createElement("group",{ref:f,matrixAutoUpdate:!1},e))}));exports.Float=i;
