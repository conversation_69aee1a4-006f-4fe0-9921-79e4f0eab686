"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("@react-three/fiber"),a=require("three");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var o=n(e),u=i(t),c=i(a);let l=function(e){return e.Linear="linear",e.Radial="radial",e}({});exports.GradientTexture=function({stops:e,colors:t,size:a=1024,width:n=16,type:i=l.Linear,innerCircleRadius:s=0,outerCircleRadius:d="auto",...f}){const h=r.useThree((e=>e.gl)),p=u.useMemo((()=>{const r=document.createElement("canvas"),o=r.getContext("2d");let u;if(r.width=n,r.height=a,i===l.Linear)u=o.createLinearGradient(0,0,0,a);else{const e=r.width/2,t=r.height/2,a="auto"!==d?Math.abs(Number(d)):Math.sqrt(e**2+t**2);u=o.createRadialGradient(e,t,Math.abs(s),e,t,a)}const f=new c.Color;let h=e.length;for(;h--;)u.addColorStop(e[h],f.set(t[h]).getStyle());return o.save(),o.fillStyle=u,o.fillRect(0,0,n,a),o.restore(),r}),[e]);return u.createElement("canvasTexture",o.default({colorSpace:h.outputColorSpace,args:[p],attach:"map"},f))},exports.GradientType=l;
