"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("three"),t=require("@react-three/fiber");function n(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var c=n(e);exports.useAnimations=function(e,n){const u=c.useRef(null),[o]=c.useState((()=>n?n instanceof r.Object3D?{current:n}:n:u)),[a]=c.useState((()=>new r.AnimationMixer(void 0)));c.useLayoutEffect((()=>{n&&(o.current=n instanceof r.Object3D?n:n.current),a._root=o.current}));const i=c.useRef({}),s=c.useMemo((()=>{const r={};return e.forEach((e=>Object.defineProperty(r,e.name,{enumerable:!0,get(){if(o.current)return i.current[e.name]||(i.current[e.name]=a.clipAction(e,o.current))},configurable:!0}))),{ref:o,clips:e,actions:r,names:e.map((e=>e.name)),mixer:a}}),[e]);return t.useFrame(((e,r)=>a.update(r))),c.useEffect((()=>{const e=o.current;return()=>{i.current={},a.stopAllAction(),Object.values(s.actions).forEach((r=>{e&&a.uncacheAction(r,e)}))}}),[e]),s};
