"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("@react-three/fiber"),u=require("./Fbo.cjs.js");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function c(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}require("three");var a=n(e),s=c(r);const l=s.forwardRef((({envMap:e,resolution:r=256,frames:n=1/0,makeDefault:c,children:l,...o},f)=>{const i=t.useThree((({set:e})=>e)),d=t.useThree((({camera:e})=>e)),b=t.useThree((({size:e})=>e)),p=s.useRef(null);s.useImperativeHandle(f,(()=>p.current),[]);const m=s.useRef(null),g=u.useFBO(r);s.useLayoutEffect((()=>{o.manual||(p.current.aspect=b.width/b.height)}),[b,o]),s.useLayoutEffect((()=>{p.current.updateProjectionMatrix()}));let h=0,v=null;const j="function"==typeof l;return t.useFrame((r=>{j&&(n===1/0||h<n)&&(m.current.visible=!1,r.gl.setRenderTarget(g),v=r.scene.background,e&&(r.scene.background=e),r.gl.render(r.scene,p.current),r.scene.background=v,r.gl.setRenderTarget(null),m.current.visible=!0,h++)})),s.useLayoutEffect((()=>{if(c){const e=d;return i((()=>({camera:p.current}))),()=>i((()=>({camera:e})))}}),[p,c,i]),s.createElement(s.Fragment,null,s.createElement("perspectiveCamera",a.default({ref:p},o),!j&&l),s.createElement("group",{ref:m},j&&l(g.texture)))}));exports.PerspectiveCamera=l;
