"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("three"),r=require("react"),n=require("@react-three/fiber"),o=require("camera-controls");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function a(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var s=l(e),u=a(r),c=l(o);const i=r.forwardRef(((e,o)=>{const{impl:l,camera:a,domElement:i,makeDefault:d,onControlStart:f,onControl:m,onControlEnd:v,onTransitionStart:p,onUpdate:E,onWake:h,onRest:L,onSleep:b,onStart:C,onEnd:T,onChange:g,regress:j,...x}=e,y=null!=l?l:c.default;r.useMemo((()=>{const e={Box3:t.Box3,MathUtils:{clamp:t.MathUtils.clamp},Matrix4:t.Matrix4,Quaternion:t.Quaternion,Raycaster:t.Raycaster,Sphere:t.Sphere,Spherical:t.Spherical,Vector2:t.Vector2,Vector3:t.Vector3,Vector4:t.Vector4};y.install({THREE:e}),n.extend({CameraControlsImpl:y})}),[y]);const M=n.useThree((e=>e.camera)),O=n.useThree((e=>e.gl)),S=n.useThree((e=>e.invalidate)),V=n.useThree((e=>e.events)),k=n.useThree((e=>e.setEvents)),q=n.useThree((e=>e.set)),w=n.useThree((e=>e.get)),R=n.useThree((e=>e.performance)),P=a||M,_=i||V.connected||O.domElement,U=r.useMemo((()=>new y(P)),[y,P]);return n.useFrame(((e,t)=>{U.update(t)}),-1),r.useEffect((()=>(U.connect(_),()=>{U.disconnect()})),[_,U]),r.useEffect((()=>{function e(){S(),j&&R.regress()}const t=t=>{e(),null==f||f(t),null==C||C(t)},r=t=>{e(),null==m||m(t),null==g||g(t)},n=e=>{null==v||v(e),null==T||T(e)},o=t=>{e(),null==p||p(t),null==g||g(t)},l=t=>{e(),null==E||E(t),null==g||g(t)},a=t=>{e(),null==h||h(t),null==g||g(t)},s=e=>{null==L||L(e)},u=e=>{null==b||b(e)};return U.addEventListener("controlstart",t),U.addEventListener("control",r),U.addEventListener("controlend",n),U.addEventListener("transitionstart",o),U.addEventListener("update",l),U.addEventListener("wake",a),U.addEventListener("rest",s),U.addEventListener("sleep",u),()=>{U.removeEventListener("controlstart",t),U.removeEventListener("control",r),U.removeEventListener("controlend",n),U.removeEventListener("transitionstart",o),U.removeEventListener("update",l),U.removeEventListener("wake",a),U.removeEventListener("rest",s),U.removeEventListener("sleep",u)}}),[U,S,k,j,R,f,m,v,p,E,h,L,b,g,C,T]),r.useEffect((()=>{if(d){const e=w().controls;return q({controls:U}),()=>q({controls:e})}}),[d,U]),u.createElement("primitive",s.default({ref:o,object:U},x))}));Object.defineProperty(exports,"CameraControlsImpl",{enumerable:!0,get:function(){return c.default}}),exports.CameraControls=i;
