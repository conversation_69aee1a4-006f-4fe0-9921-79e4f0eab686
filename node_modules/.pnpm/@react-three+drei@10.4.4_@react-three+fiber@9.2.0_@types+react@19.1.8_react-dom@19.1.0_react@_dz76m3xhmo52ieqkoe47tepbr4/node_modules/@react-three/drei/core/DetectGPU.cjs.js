"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("detect-gpu"),r=require("suspend-react");function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var u=n(e);const c=e=>r.suspend((()=>t.getGPUTier(e)),["useDetectGPU"]);exports.DetectGPU=function({children:e,...t}){const r=c(t);return u.createElement(u.Fragment,null,null==e?void 0:e(r))},exports.useDetectGPU=c;
