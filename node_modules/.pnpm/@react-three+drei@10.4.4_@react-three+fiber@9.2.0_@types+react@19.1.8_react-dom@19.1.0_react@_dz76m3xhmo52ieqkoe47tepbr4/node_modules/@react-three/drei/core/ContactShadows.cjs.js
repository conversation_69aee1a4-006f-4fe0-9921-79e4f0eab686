"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("three"),a=require("@react-three/fiber"),n=require("three-stdlib");function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var a=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,a.get?a:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var i=o(e),l=u(r),s=u(t);const c=l.forwardRef((({scale:e=10,frames:r=1/0,opacity:t=1,width:o=1,height:u=1,blur:c=1,near:d=0,far:f=10,resolution:h=512,smooth:m=!0,color:p="#000000",depthWrite:g=!1,renderOrder:v,...b},M)=>{const y=l.useRef(null),w=a.useThree((e=>e.scene)),T=a.useThree((e=>e.gl)),x=l.useRef(null);o*=Array.isArray(e)?e[0]:e||1,u*=Array.isArray(e)?e[1]:e||1;const[O,R,S,j,C,P,q]=l.useMemo((()=>{const e=new s.WebGLRenderTarget(h,h),r=new s.WebGLRenderTarget(h,h);r.texture.generateMipmaps=e.texture.generateMipmaps=!1;const t=new s.PlaneGeometry(o,u).rotateX(Math.PI/2),a=new s.Mesh(t),i=new s.MeshDepthMaterial;i.depthTest=i.depthWrite=!1,i.onBeforeCompile=e=>{e.uniforms={...e.uniforms,ucolor:{value:new s.Color(p)}},e.fragmentShader=e.fragmentShader.replace("void main() {","uniform vec3 ucolor;\n           void main() {\n          "),e.fragmentShader=e.fragmentShader.replace("vec4( vec3( 1.0 - fragCoordZ ), opacity );","vec4( ucolor * fragCoordZ * 2.0, ( 1.0 - fragCoordZ ) * 1.0 );")};const l=new s.ShaderMaterial(n.HorizontalBlurShader),c=new s.ShaderMaterial(n.VerticalBlurShader);return c.depthTest=l.depthTest=!1,[e,t,i,a,l,c,r]}),[h,o,u,e,p]),E=e=>{j.visible=!0,j.material=C,C.uniforms.tDiffuse.value=O.texture,C.uniforms.h.value=1*e/256,T.setRenderTarget(q),T.render(j,x.current),j.material=P,P.uniforms.tDiffuse.value=q.texture,P.uniforms.v.value=1*e/256,T.setRenderTarget(O),T.render(j,x.current),j.visible=!1};let W,k,A=0;return a.useFrame((()=>{x.current&&(r===1/0||A<r)&&(A++,W=w.background,k=w.overrideMaterial,y.current.visible=!1,w.background=null,w.overrideMaterial=S,T.setRenderTarget(O),T.render(w,x.current),E(c),m&&E(.4*c),T.setRenderTarget(null),y.current.visible=!0,w.overrideMaterial=k,w.background=W)})),l.useImperativeHandle(M,(()=>y.current),[]),l.createElement("group",i.default({"rotation-x":Math.PI/2},b,{ref:y}),l.createElement("mesh",{renderOrder:v,geometry:R,scale:[1,-1,1],rotation:[-Math.PI/2,0,0]},l.createElement("meshBasicMaterial",{transparent:!0,map:O.texture,opacity:t,depthWrite:g})),l.createElement("orthographicCamera",{ref:x,args:[-o/2,o/2,u/2,-u/2,d,f]}))}));exports.ContactShadows=c;
