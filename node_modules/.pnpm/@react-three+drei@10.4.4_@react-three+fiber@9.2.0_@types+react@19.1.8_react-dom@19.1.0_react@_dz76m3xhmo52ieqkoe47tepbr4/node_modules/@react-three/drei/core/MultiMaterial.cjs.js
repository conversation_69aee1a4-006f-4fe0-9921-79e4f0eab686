"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react");function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var n=t(e),a=u(r);exports.MultiMaterial=function(e){const r=a.useRef(null);return a.useLayoutEffect((()=>{const e=r.current.parent,t=null==e?void 0:e.geometry;if(t){const u=e.material;e.material=r.current.__r3f.children.map((e=>e.object));const n=[...t.groups];return t.clearGroups(),e.material.forEach(((r,u)=>{u<e.material.length-1&&(r.depthWrite=!1),t.addGroup(0,1/0,u)})),()=>{e.material=u,t.groups=n}}})),a.createElement("group",n.default({ref:r},e))};
