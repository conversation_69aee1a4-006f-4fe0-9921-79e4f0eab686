"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),a=require("@react-three/fiber"),n=require("../materials/BlurPass.cjs.js"),o=require("../materials/MeshReflectorMaterial.cjs.js");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("../materials/ConvolutionMaterial.cjs.js"),require("../helpers/constants.cjs.js");var l=s(e),u=i(t);const d=u.forwardRef((({mixBlur:e=0,mixStrength:t=1,resolution:s=256,blur:i=[0,0],minDepthThreshold:d=.9,maxDepthThreshold:c=1,depthScale:p=0,depthToBlurRatioBias:m=.25,mirror:h=0,distortion:f=1,mixContrast:x=1,distortionMap:M,reflectorOffset:T=0,...S},y)=>{a.extend({MeshReflectorMaterialImpl:o.MeshReflectorMaterial});const b=a.useThree((({gl:e})=>e)),w=a.useThree((({camera:e})=>e)),j=a.useThree((({scene:e})=>e)),R=(i=Array.isArray(i)?i:[i,i])[0]+i[1]>0,v=i[0],g=i[1],D=u.useRef(null);u.useImperativeHandle(y,(()=>D.current),[]);const[B]=u.useState((()=>new r.Plane)),[_]=u.useState((()=>new r.Vector3)),[O]=u.useState((()=>new r.Vector3)),[P]=u.useState((()=>new r.Vector3)),[U]=u.useState((()=>new r.Matrix4)),[V]=u.useState((()=>new r.Vector3(0,0,-1))),[E]=u.useState((()=>new r.Vector4)),[F]=u.useState((()=>new r.Vector3)),[W]=u.useState((()=>new r.Vector3)),[I]=u.useState((()=>new r.Vector4)),[q]=u.useState((()=>new r.Matrix4)),[C]=u.useState((()=>new r.PerspectiveCamera)),k=u.useCallback((()=>{var e;const t=D.current.parent||(null==(e=D.current)||null==(e=e.__r3f.parent)?void 0:e.object);if(!t)return;if(O.setFromMatrixPosition(t.matrixWorld),P.setFromMatrixPosition(w.matrixWorld),U.extractRotation(t.matrixWorld),_.set(0,0,1),_.applyMatrix4(U),O.addScaledVector(_,T),F.subVectors(O,P),F.dot(_)>0)return;F.reflect(_).negate(),F.add(O),U.extractRotation(w.matrixWorld),V.set(0,0,-1),V.applyMatrix4(U),V.add(P),W.subVectors(O,V),W.reflect(_).negate(),W.add(O),C.position.copy(F),C.up.set(0,1,0),C.up.applyMatrix4(U),C.up.reflect(_),C.lookAt(W),C.far=w.far,C.updateMatrixWorld(),C.projectionMatrix.copy(w.projectionMatrix),q.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),q.multiply(C.projectionMatrix),q.multiply(C.matrixWorldInverse),q.multiply(t.matrixWorld),B.setFromNormalAndCoplanarPoint(_,O),B.applyMatrix4(C.matrixWorldInverse),E.set(B.normal.x,B.normal.y,B.normal.z,B.constant);const r=C.projectionMatrix;I.x=(Math.sign(E.x)+r.elements[8])/r.elements[0],I.y=(Math.sign(E.y)+r.elements[9])/r.elements[5],I.z=-1,I.w=(1+r.elements[10])/r.elements[14],E.multiplyScalar(2/E.dot(I)),r.elements[2]=E.x,r.elements[6]=E.y,r.elements[10]=E.z+1,r.elements[14]=E.w}),[w,T]),[L,z,A,H]=u.useMemo((()=>{const a={minFilter:r.LinearFilter,magFilter:r.LinearFilter,type:r.HalfFloatType},o=new r.WebGLRenderTarget(s,s,a);o.depthBuffer=!0,o.depthTexture=new r.DepthTexture(s,s),o.depthTexture.format=r.DepthFormat,o.depthTexture.type=r.UnsignedShortType;const i=new r.WebGLRenderTarget(s,s,a);return[o,i,new n.BlurPass({gl:b,resolution:s,width:v,height:g,minDepthThreshold:d,maxDepthThreshold:c,depthScale:p,depthToBlurRatioBias:m}),{mirror:h,textureMatrix:q,mixBlur:e,tDiffuse:o.texture,tDepth:o.depthTexture,tDiffuseBlur:i.texture,hasBlur:R,mixStrength:t,minDepthThreshold:d,maxDepthThreshold:c,depthScale:p,depthToBlurRatioBias:m,distortion:f,distortionMap:M,mixContrast:x,"defines-USE_BLUR":R?"":void 0,"defines-USE_DEPTH":p>0?"":void 0,"defines-USE_DISTORTION":M?"":void 0}]}),[b,v,g,q,s,h,R,e,t,d,c,p,m,f,M,x]);return a.useFrame((()=>{var e;const t=D.current.parent||(null==(e=D.current)||null==(e=e.__r3f.parent)?void 0:e.object);if(!t)return;t.visible=!1;const r=b.xr.enabled,a=b.shadowMap.autoUpdate;k(),b.xr.enabled=!1,b.shadowMap.autoUpdate=!1,b.setRenderTarget(L),b.state.buffers.depth.setMask(!0),b.autoClear||b.clear(),b.render(j,C),R&&A.render(b,L,z),b.xr.enabled=r,b.shadowMap.autoUpdate=a,t.visible=!0,b.setRenderTarget(null)})),u.createElement("meshReflectorMaterialImpl",l.default({attach:"material",key:"key"+H["defines-USE_BLUR"]+H["defines-USE_DEPTH"]+H["defines-USE_DISTORTION"],ref:D},H,S))}));exports.MeshReflectorMaterial=d;
