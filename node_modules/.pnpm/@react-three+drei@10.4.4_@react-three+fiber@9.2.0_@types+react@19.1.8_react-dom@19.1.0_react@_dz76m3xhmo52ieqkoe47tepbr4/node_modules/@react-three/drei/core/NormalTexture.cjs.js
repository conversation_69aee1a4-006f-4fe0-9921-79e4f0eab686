"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("./Texture.cjs.js"),t=require("three"),n=require("suspend-react");function s(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}require("@react-three/fiber");var o=s(e);function a(e=0,s={},a){const{repeat:u=[1,1],anisotropy:c=1,offset:i=[0,0]}=s,f=n.suspend((()=>fetch("https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master/normals/normals.json").then((e=>e.json()))),["normalsList"]),l=o.useMemo((()=>Object.keys(f).length),[]),p=f[0],d=`https://rawcdn.githack.com/pmndrs/drei-assets/7a3104997e1576f83472829815b00880d88b32fb/normals/${f[e]||p}`,m=r.useTexture(d,a);return o.useLayoutEffect((()=>{m&&(m.wrapS=m.wrapT=t.RepeatWrapping,m.repeat=new t.Vector2(u[0],u[1]),m.offset=new t.Vector2(i[0],i[1]),m.anisotropy=c)}),[m,c,u,i]),[m,d,l]}exports.NormalTexture=({children:e,id:r,onLoad:t,...n})=>{const s=a(r,n,t);return o.createElement(o.Fragment,null,null==e?void 0:e(s))},exports.useNormalTexture=a;
