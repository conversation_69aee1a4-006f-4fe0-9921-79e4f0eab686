"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("three"),n=require("@react-three/fiber");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function c(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var a=u(e),o=c(r),i=c(t);const s=new i.Box3,f=new i.Vector3;exports.BBAnchor=({anchor:e,...r})=>{const t=o.useRef(null),u=o.useRef(null);return o.useEffect((()=>{var e;null!=(e=t.current)&&null!=(e=e.parent)&&e.parent&&(u.current=t.current.parent,t.current.parent.parent.add(t.current))}),[]),n.useFrame((()=>{u.current&&(s.setFromObject(u.current),s.getSize(f),t.current.position.set(u.current.position.x+f.x*(Array.isArray(e)?e[0]:e.x)/2,u.current.position.y+f.y*(Array.isArray(e)?e[1]:e.y)/2,u.current.position.z+f.z*(Array.isArray(e)?e[2]:e.z)/2))})),o.createElement("group",a.default({ref:t},r))};
