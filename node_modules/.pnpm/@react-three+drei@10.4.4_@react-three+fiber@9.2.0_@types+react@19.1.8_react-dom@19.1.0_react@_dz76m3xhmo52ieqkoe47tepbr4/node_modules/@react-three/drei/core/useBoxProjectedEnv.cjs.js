"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("three"),r=require("react"),n=require("@react-three/fiber");function i(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var i=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(r,n,i.get?i:{enumerable:!0,get:function(){return e[n]}})}})),r.default=e,Object.freeze(r)}var o=i(e),t=i(r);exports.useBoxProjectedEnv=function(e=new o.Vector3,r=new o.Vector3){const[i]=t.useState((()=>({position:new o.Vector3,size:new o.Vector3})));n.applyProps(i,{position:e,size:r});const a=t.useRef(null),c=t.useMemo((()=>({ref:a,onBeforeCompile:e=>function(e,r,n){e.defines.BOX_PROJECTED_ENV_MAP=!0,e.uniforms.envMapPosition={value:r},e.uniforms.envMapSize={value:n},e.vertexShader=`\n  varying vec3 vWorldPosition;\n  ${e.vertexShader.replace("#include <worldpos_vertex>","\n#if defined( USE_ENVMAP ) || defined( DISTANCE ) || defined ( USE_SHADOWMAP )\n  vec4 worldPosition = modelMatrix * vec4( transformed, 1.0 );\n  #ifdef BOX_PROJECTED_ENV_MAP\n    vWorldPosition = worldPosition.xyz;\n  #endif\n#endif\n")}`,e.fragmentShader=`\n    \n#ifdef BOX_PROJECTED_ENV_MAP\n  uniform vec3 envMapSize;\n  uniform vec3 envMapPosition;\n  varying vec3 vWorldPosition;\n    \n  vec3 parallaxCorrectNormal( vec3 v, vec3 cubeSize, vec3 cubePos ) {\n    vec3 nDir = normalize( v );\n    vec3 rbmax = ( .5 * cubeSize + cubePos - vWorldPosition ) / nDir;\n    vec3 rbmin = ( -.5 * cubeSize + cubePos - vWorldPosition ) / nDir;\n    vec3 rbminmax;\n    rbminmax.x = ( nDir.x > 0. ) ? rbmax.x : rbmin.x;\n    rbminmax.y = ( nDir.y > 0. ) ? rbmax.y : rbmin.y;\n    rbminmax.z = ( nDir.z > 0. ) ? rbmax.z : rbmin.z;\n    float correction = min( min( rbminmax.x, rbminmax.y ), rbminmax.z );\n    vec3 boxIntersection = vWorldPosition + nDir * correction;    \n    return boxIntersection - cubePos;\n  }\n#endif\n\n    ${e.fragmentShader.replace("#include <envmap_physical_pars_fragment>",o.ShaderChunk.envmap_physical_pars_fragment).replace("vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );","vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\n         \n#ifdef BOX_PROJECTED_ENV_MAP\n  worldNormal = parallaxCorrectNormal( worldNormal, envMapSize, envMapPosition );\n#endif\n\n         ").replace("reflectVec = inverseTransformDirection( reflectVec, viewMatrix );","reflectVec = inverseTransformDirection( reflectVec, viewMatrix );\n         \n#ifdef BOX_PROJECTED_ENV_MAP\n  reflectVec = parallaxCorrectNormal( reflectVec, envMapSize, envMapPosition );\n#endif\n\n        ")}`}(e,i.position,i.size),customProgramCacheKey:()=>JSON.stringify(i.position.toArray())+JSON.stringify(i.size.toArray())})),[...i.position.toArray(),...i.size.toArray()]);return t.useLayoutEffect((()=>{a.current.needsUpdate=!0}),[i]),c};
