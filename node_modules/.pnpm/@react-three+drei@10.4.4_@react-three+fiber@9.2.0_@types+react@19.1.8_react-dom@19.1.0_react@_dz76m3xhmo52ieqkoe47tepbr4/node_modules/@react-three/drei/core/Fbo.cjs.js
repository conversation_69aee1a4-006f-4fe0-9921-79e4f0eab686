"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("three"),r=require("@react-three/fiber");function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var u=n(e),i=n(t);function o(e,t,n){const o=r.useThree((e=>e.size)),s=r.useThree((e=>e.viewport)),a="number"==typeof e?e:o.width*s.dpr,l="number"==typeof t?t:o.height*s.dpr,f=("number"==typeof e?n:e)||{},{samples:p=0,depth:c,...d}=f,h=null!=c?c:f.depthBuffer,b=u.useMemo((()=>{const e=new i.WebGLRenderTarget(a,l,{minFilter:i.LinearFilter,magFilter:i.LinearFilter,type:i.HalfFloatType,...d});return h&&(e.depthTexture=new i.DepthTexture(a,l,i.FloatType)),e.samples=p,e}),[]);return u.useLayoutEffect((()=>{b.setSize(a,l),p&&(b.samples=p)}),[p,b,a,l]),u.useEffect((()=>()=>b.dispose()),[]),b}const s=e.forwardRef((({children:t,width:r,height:n,...i},s)=>{const a=o(r,n,i);return e.useImperativeHandle(s,(()=>a),[a]),u.createElement(u.Fragment,null,null==t?void 0:t(a))}));exports.Fbo=s,exports.useFBO=o;
