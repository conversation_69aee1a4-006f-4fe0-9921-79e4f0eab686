"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("@react-three/fiber"),t=require("react"),n=require("three-stdlib");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function o(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var a=s(e),u=o(t);const c=t.forwardRef((({camera:e,makeDefault:s,regress:o,domElement:c,onChange:i,onStart:l,onEnd:f,...d},m)=>{const v=r.useThree((e=>e.invalidate)),b=r.useThree((e=>e.camera)),h=r.useThree((e=>e.gl)),E=r.useThree((e=>e.events)),p=r.useThree((e=>e.set)),g=r.useThree((e=>e.get)),j=r.useThree((e=>e.performance)),O=e||b,T=c||E.connected||h.domElement,L=t.useMemo((()=>new n.ArcballControls(O)),[O]);return r.useFrame((()=>{L.enabled&&L.update()}),-1),t.useEffect((()=>(L.connect(T),()=>{L.dispose()})),[T,o,L,v]),t.useEffect((()=>{const e=e=>{v(),o&&j.regress(),i&&i(e)};return L.addEventListener("change",e),l&&L.addEventListener("start",l),f&&L.addEventListener("end",f),()=>{L.removeEventListener("change",e),l&&L.removeEventListener("start",l),f&&L.removeEventListener("end",f)}}),[i,l,f]),t.useEffect((()=>{if(s){const e=g().controls;return p({controls:L}),()=>p({controls:e})}}),[s,L]),u.createElement("primitive",a.default({ref:m,object:L},d))}));exports.ArcballControls=c;
