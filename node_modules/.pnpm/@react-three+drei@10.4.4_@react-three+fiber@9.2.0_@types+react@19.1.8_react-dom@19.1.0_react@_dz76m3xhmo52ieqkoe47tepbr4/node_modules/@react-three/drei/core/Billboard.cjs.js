"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("three"),n=require("@react-three/fiber");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function o(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var c=u(e),a=o(r);const i=a.forwardRef((function({children:e,follow:r=!0,lockX:u=!1,lockY:o=!1,lockZ:i=!1,...l},f){const d=a.useRef(null),s=a.useRef(null),p=new t.Quaternion;return n.useFrame((({camera:e})=>{if(!r||!s.current)return;const t=d.current.rotation.clone();s.current.updateMatrix(),s.current.updateWorldMatrix(!1,!1),s.current.getWorldQuaternion(p),e.getWorldQuaternion(d.current.quaternion).premultiply(p.invert()),u&&(d.current.rotation.x=t.x),o&&(d.current.rotation.y=t.y),i&&(d.current.rotation.z=t.z)})),a.useImperativeHandle(f,(()=>s.current),[]),a.createElement("group",c.default({ref:s},l),a.createElement("group",{ref:d},e))}));exports.Billboard=i;
