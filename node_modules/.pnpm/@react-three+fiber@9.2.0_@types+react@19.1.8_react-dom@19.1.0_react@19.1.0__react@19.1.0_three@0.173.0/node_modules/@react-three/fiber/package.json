{"name": "@react-three/fiber", "version": "9.2.0", "description": "A React renderer for Threejs", "keywords": ["react", "renderer", "fiber", "three", "threejs"], "author": "<PERSON> (https://github.com/drcmda)", "license": "MIT", "maintainers": ["<PERSON> (https://github.com/joshua<PERSON>s)", "<PERSON> (https://github.com/cody<PERSON><PERSON><PERSON>)", "<PERSON> (https://github.com/krispya)"], "bugs": {"url": "https://github.com/pmndrs/react-three-fiber/issues"}, "homepage": "https://github.com/pmndrs/react-three-fiber#readme", "repository": {"type": "git", "url": "git+https://github.com/pmndrs/react-three-fiber.git"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/react-three-fiber"}, "main": "dist/react-three-fiber.cjs.js", "module": "dist/react-three-fiber.esm.js", "types": "dist/react-three-fiber.cjs.d.ts", "react-native": "native/dist/react-three-fiber-native.cjs.js", "sideEffects": false, "preconstruct": {"entrypoints": ["index.tsx", "native.tsx"]}, "scripts": {"prebuild": "cp ../../readme.md readme.md"}, "dependencies": {"@babel/runtime": "^7.17.8", "@types/react-reconciler": "^0.28.9", "@types/webxr": "*", "base64-js": "^1.5.1", "buffer": "^6.0.3", "its-fine": "^2.0.0", "react-reconciler": "^0.31.0", "react-use-measure": "^2.1.7", "scheduler": "^0.25.0", "suspend-react": "^0.1.3", "use-sync-external-store": "^1.4.0", "zustand": "^5.0.3"}, "peerDependencies": {"expo": ">=43.0", "expo-asset": ">=8.4", "expo-file-system": ">=11.0", "expo-gl": ">=11.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-native": ">=0.78", "three": ">=0.156"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}, "expo": {"optional": true}, "expo-asset": {"optional": true}, "expo-file-system": {"optional": true}, "expo-gl": {"optional": true}}}