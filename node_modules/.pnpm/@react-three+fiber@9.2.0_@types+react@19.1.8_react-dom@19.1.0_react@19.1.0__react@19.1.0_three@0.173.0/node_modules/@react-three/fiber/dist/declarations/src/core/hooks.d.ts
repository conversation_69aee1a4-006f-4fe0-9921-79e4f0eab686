import * as THREE from 'three';
import * as React from 'react';
import { RootState, RenderCallback, RootStore } from "./store.js";
import { ObjectMap } from "./utils.js";
import type { Instance, ConstructorRepresentation } from "./reconciler.js";
/**
 * Exposes an object's {@link Instance}.
 * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useInstanceHandle
 *
 * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.
 */
export declare function useInstanceHandle<T>(ref: React.RefObject<T>): React.RefObject<Instance<T>>;
/**
 * Returns the R3F Canvas' Zustand store. Useful for [transient updates](https://github.com/pmndrs/zustand#transient-updates-for-often-occurring-state-changes).
 * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usestore
 */
export declare function useStore(): RootStore;
/**
 * Accesses R3F's internal state, containing renderer, canvas, scene, etc.
 * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree
 */
export declare function useThree<T = RootState>(selector?: (state: RootState) => T, equalityFn?: <T>(state: T, newState: T) => boolean): T;
/**
 * Executes a callback before render in a shared frame loop.
 * Can order effects with render priority or manually render with a positive priority.
 * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe
 */
export declare function useFrame(callback: RenderCallback, renderPriority?: number): null;
/**
 * Returns a node graph of an object with named nodes & materials.
 * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph
 */
export declare function useGraph(object: THREE.Object3D): ObjectMap;
type InputLike = string | string[] | string[][] | Readonly<string | string[] | string[][]>;
type LoaderLike = THREE.Loader<any, InputLike>;
type GLTFLike = {
    scene: THREE.Object3D;
};
type LoaderInstance<T extends LoaderLike | ConstructorRepresentation<LoaderLike>> = T extends ConstructorRepresentation<LoaderLike> ? InstanceType<T> : T;
export type LoaderResult<T extends LoaderLike | ConstructorRepresentation<LoaderLike>> = Awaited<ReturnType<LoaderInstance<T>['loadAsync']>> extends infer R ? R extends GLTFLike ? R & ObjectMap : R : never;
export type Extensions<T extends LoaderLike | ConstructorRepresentation<LoaderLike>> = (loader: LoaderInstance<T>) => void;
/**
 * Synchronously loads and caches assets with a three loader.
 *
 * Note: this hook's caller must be wrapped with `React.Suspense`
 * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader
 */
export declare function useLoader<I extends InputLike, L extends LoaderLike | ConstructorRepresentation<LoaderLike>>(loader: L, input: I, extensions?: Extensions<L>, onProgress?: (event: ProgressEvent<EventTarget>) => void): I extends any[] ? LoaderResult<L>[] : LoaderResult<L>;
export declare namespace useLoader {
    var preload: <I extends InputLike, L extends LoaderLike | ConstructorRepresentation<LoaderLike>>(loader: L, input: I, extensions?: Extensions<L> | undefined) => void;
    var clear: <I extends InputLike, L extends LoaderLike | ConstructorRepresentation<LoaderLike>>(loader: L, input: I) => void;
}
export {};
