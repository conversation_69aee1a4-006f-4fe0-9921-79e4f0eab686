#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/Workspace/Melpa/langitbiru-event/node_modules/.pnpm/vite@6.3.5_jiti@2.4.2_lightningcss@1.30.1/node_modules/vite/bin/node_modules:/home/<USER>/Workspace/Melpa/langitbiru-event/node_modules/.pnpm/vite@6.3.5_jiti@2.4.2_lightningcss@1.30.1/node_modules/vite/node_modules:/home/<USER>/Workspace/Melpa/langitbiru-event/node_modules/.pnpm/vite@6.3.5_jiti@2.4.2_lightningcss@1.30.1/node_modules:/home/<USER>/Workspace/Melpa/langitbiru-event/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/Workspace/Melpa/langitbiru-event/node_modules/.pnpm/vite@6.3.5_jiti@2.4.2_lightningcss@1.30.1/node_modules/vite/bin/node_modules:/home/<USER>/Workspace/Melpa/langitbiru-event/node_modules/.pnpm/vite@6.3.5_jiti@2.4.2_lightningcss@1.30.1/node_modules/vite/node_modules:/home/<USER>/Workspace/Melpa/langitbiru-event/node_modules/.pnpm/vite@6.3.5_jiti@2.4.2_lightningcss@1.30.1/node_modules:/home/<USER>/Workspace/Melpa/langitbiru-event/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../vite/bin/vite.js" "$@"
fi
