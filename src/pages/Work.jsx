import React, { useState } from 'react';

const Work = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Projects' },
    { id: 'corporate', name: 'Corporate' },
    { id: 'wedding', name: 'Weddings' },
    { id: 'social', name: 'Social Events' },
    { id: 'cultural', name: 'Cultural' }
  ];

  const projects = [
    {
      id: 1,
      title: "Tech Summit 2024",
      category: "corporate",
      description: "Annual technology conference for 500+ attendees with keynote speakers and networking sessions.",
      image: "/api/placeholder/400/300",
      tags: ["Conference", "Technology", "Networking"]
    },
    {
      id: 2,
      title: "<PERSON> <PERSON> <PERSON>'s Wedding",
      category: "wedding",
      description: "Elegant garden wedding ceremony and reception for 150 guests with custom floral arrangements.",
      image: "/api/placeholder/400/300",
      tags: ["Garden Wedding", "Elegant", "Custom Design"]
    },
    {
      id: 3,
      title: "Company Anniversary Gala",
      category: "corporate",
      description: "25th anniversary celebration with awards ceremony, entertainment, and formal dinner.",
      image: "/api/placeholder/400/300",
      tags: ["Anniversary", "Gala", "Awards"]
    },
    {
      id: 4,
      title: "Cultural Heritage Festival",
      category: "cultural",
      description: "Three-day cultural festival celebrating local traditions with performances and exhibitions.",
      image: "/api/placeholder/400/300",
      tags: ["Festival", "Culture", "Community"]
    },
    {
      id: 5,
      title: "Birthday Celebration",
      category: "social",
      description: "Milestone 50th birthday party with themed decorations and entertainment for 80 guests.",
      image: "/api/placeholder/400/300",
      tags: ["Birthday", "Milestone", "Themed"]
    },
    {
      id: 6,
      title: "Product Launch Event",
      category: "corporate",
      description: "High-profile product launch with media coverage, demonstrations, and VIP reception.",
      image: "/api/placeholder/400/300",
      tags: ["Product Launch", "Media", "VIP"]
    }
  ];

  const filteredProjects = selectedCategory === 'all' 
    ? projects 
    : projects.filter(project => project.category === selectedCategory);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20">
        <div className="container mx-auto px-6 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">Our Work</h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto">
            Explore our portfolio of successful events and celebrations that showcase our creativity and expertise
          </p>
        </div>
      </section>

      {/* Filter Section */}
      <section className="container mx-auto px-6 py-12">
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-6 py-3 rounded-full font-semibold transition-colors ${
                selectedCategory === category.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-blue-50 border border-gray-300'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project) => (
            <div key={project.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              <div className="h-48 bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center text-white text-2xl font-bold">
                {project.title.charAt(0)}
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-3">{project.title}</h3>
                <p className="text-gray-600 mb-4">{project.description}</p>
                <div className="flex flex-wrap gap-2">
                  {project.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-100 text-blue-600 text-sm rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-blue-600 text-white py-16">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold mb-2">500+</div>
              <div className="text-xl">Events Completed</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">50,000+</div>
              <div className="text-xl">Happy Attendees</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">8+</div>
              <div className="text-xl">Years Experience</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">98%</div>
              <div className="text-xl">Client Satisfaction</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-6 py-16 text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Ready to Create Your Event?</h2>
        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          Let's work together to bring your vision to life and create an unforgettable experience
        </p>
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg text-lg font-semibold transition-colors">
          Start Your Project
        </button>
      </section>
    </div>
  );
};

export default Work;
