import React from 'react';

const Services = () => {
  const services = [
    {
      title: "Corporate Events",
      description: "Professional corporate gatherings, conferences, and business meetings tailored to your company's needs.",
      features: ["Conference Planning", "Team Building", "Product Launches", "Board Meetings"],
      icon: "🏢"
    },
    {
      title: "Wedding Planning",
      description: "Complete wedding planning services to make your special day perfect and unforgettable.",
      features: ["Venue Selection", "Catering Coordination", "Photography", "Decoration"],
      icon: "💒"
    },
    {
      title: "Social Events",
      description: "Birthday parties, anniversaries, and other social celebrations designed to create lasting memories.",
      features: ["Birthday Parties", "Anniversaries", "Graduation Parties", "Family Reunions"],
      icon: "🎉"
    },
    {
      title: "Cultural Events",
      description: "Traditional and cultural celebrations that honor heritage and bring communities together.",
      features: ["Festival Organization", "Cultural Shows", "Art Exhibitions", "Community Events"],
      icon: "🎭"
    },
    {
      title: "Virtual Events",
      description: "Modern virtual and hybrid event solutions for the digital age.",
      features: ["Online Conferences", "Webinars", "Virtual Meetings", "Hybrid Events"],
      icon: "💻"
    },
    {
      title: "Event Consulting",
      description: "Expert consultation and planning advice for any type of event or celebration.",
      features: ["Event Strategy", "Budget Planning", "Vendor Coordination", "Timeline Management"],
      icon: "💡"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20">
        <div className="container mx-auto px-6 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">Our Services</h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto">
            Comprehensive event planning services tailored to your unique needs and vision
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="container mx-auto px-6 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div key={index} className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
              <div className="text-4xl mb-4">{service.icon}</div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">{service.title}</h3>
              <p className="text-gray-600 mb-6">{service.description}</p>
              <ul className="space-y-2">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center text-gray-700">
                    <svg className="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 text-white py-16">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Plan Your Event?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Let's discuss your vision and create an unforgettable experience together
          </p>
          <button className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 rounded-lg text-lg font-semibold transition-colors">
            Get Free Consultation
          </button>
        </div>
      </section>
    </div>
  );
};

export default Services;
