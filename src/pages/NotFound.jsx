import React from 'react';
import { Link } from 'react-router-dom';

const NotFound = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="text-center px-6">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="text-9xl font-bold text-blue-600 mb-4">404</div>
          <div className="text-6xl mb-4">🎭</div>
        </div>

        {/* Error Message */}
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
          Oops! Page Not Found
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          It looks like the page you're looking for doesn't exist. 
          Don't worry, even the best events sometimes have unexpected moments!
        </p>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
          <Link
            to="/"
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg text-lg font-semibold transition-colors inline-block"
          >
            Go Home
          </Link>
          <Link
            to="/contact"
            className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-3 rounded-lg text-lg font-semibold transition-colors inline-block"
          >
            Contact Us
          </Link>
        </div>

        {/* Helpful Links */}
        <div className="bg-white rounded-xl shadow-lg p-8 max-w-md mx-auto">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Maybe you're looking for:</h2>
          <ul className="space-y-3 text-left">
            <li>
              <Link
                to="/services"
                className="text-blue-600 hover:text-blue-700 hover:underline flex items-center gap-2"
              >
                <span>🎪</span>
                Our Services
              </Link>
            </li>
            <li>
              <Link
                to="/work"
                className="text-blue-600 hover:text-blue-700 hover:underline flex items-center gap-2"
              >
                <span>📸</span>
                Our Work Portfolio
              </Link>
            </li>
            <li>
              <Link
                to="/about"
                className="text-blue-600 hover:text-blue-700 hover:underline flex items-center gap-2"
              >
                <span>👥</span>
                About Our Team
              </Link>
            </li>
            <li>
              <Link
                to="/blog"
                className="text-blue-600 hover:text-blue-700 hover:underline flex items-center gap-2"
              >
                <span>📝</span>
                Event Planning Blog
              </Link>
            </li>
          </ul>
        </div>

        {/* Fun Message */}
        <div className="mt-12 text-gray-500">
          <p className="text-sm">
            Lost? Don't worry! Even the best event planners sometimes take a wrong turn. 
            Let's get you back on track! 🎉
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
