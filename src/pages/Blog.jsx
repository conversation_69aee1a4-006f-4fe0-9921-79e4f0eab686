import React from 'react';

const Blog = () => {
  const blogPosts = [
    {
      id: 1,
      title: "10 Essential Tips for Planning Your Dream Wedding",
      excerpt: "Planning a wedding can be overwhelming, but with the right approach and timeline, you can create the perfect day without the stress.",
      author: "<PERSON>",
      date: "March 15, 2024",
      category: "Wedding Planning",
      readTime: "5 min read",
      image: "/api/placeholder/400/250"
    },
    {
      id: 2,
      title: "Corporate Event Trends for 2024",
      excerpt: "Discover the latest trends in corporate event planning, from hybrid formats to sustainable practices that are shaping the industry.",
      author: "<PERSON>",
      date: "March 10, 2024",
      category: "Corporate Events",
      readTime: "7 min read",
      image: "/api/placeholder/400/250"
    },
    {
      id: 3,
      title: "How to Choose the Perfect Venue for Your Event",
      excerpt: "The venue sets the tone for your entire event. Learn the key factors to consider when selecting the perfect location for your celebration.",
      author: "<PERSON>",
      date: "March 5, 2024",
      category: "Event Planning",
      readTime: "6 min read",
      image: "/api/placeholder/400/250"
    },
    {
      id: 4,
      title: "Budget-Friendly Event Planning Ideas",
      excerpt: "Create memorable events without breaking the bank. Discover creative ways to maximize your budget and still deliver an amazing experience.",
      author: "<PERSON>",
      date: "February 28, 2024",
      category: "Budget Planning",
      readTime: "4 min read",
      image: "/api/placeholder/400/250"
    },
    {
      id: 5,
      title: "The Rise of Virtual and Hybrid Events",
      excerpt: "Explore how virtual and hybrid events are changing the landscape of event planning and how to make them engaging for all participants.",
      author: "Michael Chen",
      date: "February 20, 2024",
      category: "Technology",
      readTime: "8 min read",
      image: "/api/placeholder/400/250"
    },
    {
      id: 6,
      title: "Sustainable Event Planning: Going Green",
      excerpt: "Learn how to plan eco-friendly events that reduce environmental impact while still creating memorable experiences for your guests.",
      author: "Emily Rodriguez",
      date: "February 15, 2024",
      category: "Sustainability",
      readTime: "6 min read",
      image: "/api/placeholder/400/250"
    }
  ];

  const categories = [
    "All Posts",
    "Wedding Planning",
    "Corporate Events",
    "Event Planning",
    "Budget Planning",
    "Technology",
    "Sustainability"
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20">
        <div className="container mx-auto px-6 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">Our Blog</h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto">
            Insights, tips, and inspiration for planning unforgettable events
          </p>
        </div>
      </section>

      {/* Blog Content */}
      <section className="container mx-auto px-6 py-16">
        <div className="flex flex-col lg:flex-row gap-12">
          {/* Main Content */}
          <div className="lg:w-2/3">
            <div className="grid gap-8">
              {blogPosts.map((post) => (
                <article key={post.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                  <div className="md:flex">
                    <div className="md:w-1/3">
                      <div className="h-48 md:h-full bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center text-white text-3xl font-bold">
                        {post.title.charAt(0)}
                      </div>
                    </div>
                    <div className="md:w-2/3 p-6">
                      <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                        <span className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full">
                          {post.category}
                        </span>
                        <span>{post.date}</span>
                        <span>{post.readTime}</span>
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900 mb-3 hover:text-blue-600 cursor-pointer">
                        {post.title}
                      </h2>
                      <p className="text-gray-600 mb-4">{post.excerpt}</p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            {post.author.charAt(0)}
                          </div>
                          <span className="text-gray-700 font-medium">{post.author}</span>
                        </div>
                        <button className="text-blue-600 hover:text-blue-700 font-semibold">
                          Read More →
                        </button>
                      </div>
                    </div>
                  </div>
                </article>
              ))}
            </div>

            {/* Pagination */}
            <div className="flex justify-center mt-12">
              <div className="flex gap-2">
                <button className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                  Previous
                </button>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg">1</button>
                <button className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">2</button>
                <button className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">3</button>
                <button className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                  Next
                </button>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:w-1/3">
            <div className="space-y-8">
              {/* Categories */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Categories</h3>
                <ul className="space-y-2">
                  {categories.map((category, index) => (
                    <li key={index}>
                      <button className="text-gray-600 hover:text-blue-600 hover:bg-blue-50 w-full text-left px-3 py-2 rounded-lg transition-colors">
                        {category}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Newsletter */}
              <div className="bg-blue-600 text-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold mb-4">Subscribe to Our Newsletter</h3>
                <p className="mb-4">Get the latest event planning tips and insights delivered to your inbox.</p>
                <div className="space-y-3">
                  <input
                    type="email"
                    placeholder="Your email address"
                    className="w-full px-4 py-2 rounded-lg text-gray-900"
                  />
                  <button className="w-full bg-white text-blue-600 hover:bg-gray-100 px-4 py-2 rounded-lg font-semibold transition-colors">
                    Subscribe
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Blog;
