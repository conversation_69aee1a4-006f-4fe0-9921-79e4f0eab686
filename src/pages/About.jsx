import React from 'react';

const About = () => {
  const team = [
    {
      name: "<PERSON>",
      role: "Founder & Creative Director",
      image: "/api/placeholder/300/300",
      bio: "With over 10 years of experience in event planning, <PERSON> brings creativity and precision to every project."
    },
    {
      name: "<PERSON>",
      role: "Operations Manager",
      image: "/api/placeholder/300/300",
      bio: "<PERSON> ensures seamless execution of events with his exceptional organizational skills and attention to detail."
    },
    {
      name: "<PERSON>",
      role: "Design Specialist",
      image: "/api/placeholder/300/300",
      bio: "<PERSON> transforms visions into reality with her innovative design concepts and artistic flair."
    }
  ];

  const values = [
    {
      title: "Excellence",
      description: "We strive for perfection in every detail, ensuring your event exceeds expectations.",
      icon: "⭐"
    },
    {
      title: "Creativity",
      description: "Innovative solutions and unique approaches make every event memorable and distinctive.",
      icon: "🎨"
    },
    {
      title: "Reliability",
      description: "Dependable service and consistent quality you can trust for your most important occasions.",
      icon: "🤝"
    },
    {
      title: "Passion",
      description: "Our love for creating extraordinary experiences drives everything we do.",
      icon: "❤️"
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">About Langit Biru</h1>
            <p className="text-xl md:text-2xl leading-relaxed">
              We are passionate event planners dedicated to creating extraordinary experiences 
              that bring people together and create lasting memories.
            </p>
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="container mx-auto px-6 py-16">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8 text-center">Our Story</h2>
          <div className="prose prose-lg mx-auto text-gray-600">
            <p className="text-lg leading-relaxed mb-6">
              Founded in 2015, Langit Biru Event began with a simple mission: to transform ordinary 
              gatherings into extraordinary experiences. What started as a small team of passionate 
              event enthusiasts has grown into a full-service event planning company trusted by 
              hundreds of clients across the region.
            </p>
            <p className="text-lg leading-relaxed mb-6">
              Our name "Langit Biru" (Blue Sky) represents our limitless creativity and optimistic 
              approach to every project. Just as the blue sky stretches endlessly above us, we 
              believe there are no limits to what we can achieve when planning your perfect event.
            </p>
            <p className="text-lg leading-relaxed">
              Today, we continue to push boundaries, embrace innovation, and deliver exceptional 
              results that exceed our clients' expectations. Every event is a new canvas for us 
              to paint unforgettable memories.
            </p>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-12 text-center">Our Values</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl mb-4">{value.icon}</div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="container mx-auto px-6 py-16">
        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-12 text-center">Meet Our Team</h2>
        <div className="grid md:grid-cols-3 gap-8">
          {team.map((member, index) => (
            <div key={index} className="text-center">
              <div className="w-48 h-48 mx-auto mb-6 rounded-full bg-gray-200 overflow-hidden">
                <div className="w-full h-full bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center text-white text-6xl font-bold">
                  {member.name.charAt(0)}
                </div>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
              <p className="text-blue-600 font-semibold mb-3">{member.role}</p>
              <p className="text-gray-600">{member.bio}</p>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default About;
