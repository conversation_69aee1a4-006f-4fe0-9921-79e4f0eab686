import { motion } from 'motion/react';

function Navigation() {
  return (
    <ul className="nav-ul">
      <li className="nav-li">
        <a className="nav-link" href="#home">
          Home
        </a>
      </li>
      <li className="nav-li">
        <a className="nav-link" href="#about">
          About
        </a>
      </li>
      <li className="nav-li">
        <a className="nav-link" href="#work">
          Work
        </a>
      </li>
      <li className="nav-li">
        <a className="nav-link" href="#contact">
          Contact
        </a>
      </li>
    </ul>
  );
}

const HeaderSmall = () => {
    return (
        <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
                type: "spring",
                stiffness: 400,
                damping: 30,
                mass: 1,
                delay: 0
            }}
            className="w-[1200px] h-[234px] grid grid-cols-2 grid-rows-2 gap-0 pt-0 relative opacity-1 overflow-visible visible fill-white"
        >
            {/* LEFT */}
            <div className="relative col-span-1 row-span-1 h-[255px] grid-cols-2 grid-rows-2 gap-x-8 gap-y-8 pl-80 opacity-1 visible fill-white overflow-visible">
                <img src="assets/logo.png" className="relative col-span-1 row-span-1 w-auto h-[123px] opacity-1 visible overflow-visible" />
            </div>

            {/* RIGHT */}
            <div className="relative col-span-1 row-span-1 w-auto h-[20px] grid-cols-2 grid-rows-2 gap-x-8 gap-y-8 pr-96 pl-96 opacity-1 visible fill-white">
                <nav className="relative col-span-1 row-span-1 w-auto h-[100%] grid-cols-2 grid-rows-2 gap-x-32 gap-y-32 p-0 opacity-1 visible">
                    <Navigation />
                </nav>
            </div>

        </motion.div>
    );
}

export default HeaderSmall;