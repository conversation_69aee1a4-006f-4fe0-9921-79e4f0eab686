const HeaderSmall = () => {
    return (
        <div className="flex-none h-auto left-0 absolute right-0 top-0 z-10">
            <nav className="items-center flex flex-row flex-no-wrap gap-0 justify-start overflow-visible relative md:w-[100%] opacity-1 h-min">
                <div className="flex items-center flex-none flex-row flex-nowrap gap-2 justify-start overflow-visible pl-20 relative w-[45%] h-min md:opacity-1">
                    <div className="flex-none overflow-visible relative w-[201px] h-[97px] aspect-[2.072936660268714/1] md:opacity-1">
                        <div className="md:absolute md:rounded-[inherit] md:inset-0">
                            <img
                                src="assets/logo.png"
                                className="md:block md:w-full md:h-full md:rounded-[inherit] md:object-center md:object-cover"
                            />
                        </div>
                    </div>
                </div>
            </nav>
        </div>
    );
}

export default HeaderSmall;