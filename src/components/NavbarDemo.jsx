import { useState } from 'react';
import NavbarItem from './NavbarItem';

const NavbarDemo = () => {
  const [currentPage, setCurrentPage] = useState('/');
  
  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Services', path: '/services' },
    { name: 'About', path: '/about' },
    { name: 'Work', path: '/work' },
    { name: 'Blog', path: '/blog' }
  ];

  return (
    <div className="min-h-screen p-8" style={{ backgroundColor: '#030412' }}>
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">
          NavbarItem Class Demo - Framer Variants
        </h1>
        
        <div className="mb-8 p-6 rounded-lg" style={{ backgroundColor: '#1a1a2e' }}>
          <h2 className="text-xl font-semibold text-white mb-4">
            Interactive Navigation with Variants
          </h2>
          <p className="text-gray-300 mb-6">
            Hover over the navigation items to see the Framer-style variants in action:
            Default → Hover IN → Hover OUT
          </p>
          
          <nav className="flex space-x-6">
            {navItems.map((item) => (
              <NavbarItem
                key={item.name}
                name={item.name}
                path={item.path}
                isCurrentPage={currentPage === item.path}
                onClick={() => setCurrentPage(item.path)}
              />
            ))}
          </nav>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="p-6 rounded-lg" style={{ backgroundColor: '#1a1a2e' }}>
            <h3 className="text-lg font-semibold text-white mb-4">
              Variant States
            </h3>
            <div className="space-y-3 text-sm">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 rounded-full" style={{ backgroundColor: '#a3a3a3' }}></div>
                <span className="text-gray-300">
                  <strong>Default:</strong> Normal state with subtle styling
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 rounded-full" style={{ backgroundColor: '#33c2cc' }}></div>
                <span className="text-gray-300">
                  <strong>Hover IN:</strong> Active hover with aqua accent
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 rounded-full" style={{ backgroundColor: '#ffffff' }}></div>
                <span className="text-gray-300">
                  <strong>Hover OUT:</strong> Smooth transition back to default
                </span>
              </div>
            </div>
          </div>

          <div className="p-6 rounded-lg" style={{ backgroundColor: '#1a1a2e' }}>
            <h3 className="text-lg font-semibold text-white mb-4">
              Class Features
            </h3>
            <ul className="space-y-2 text-sm text-gray-300">
              <li>• State management with React Class Component</li>
              <li>• Framer-style variant system</li>
              <li>• Smooth CSS transitions</li>
              <li>• Active page detection</li>
              <li>• Hover animations with transform effects</li>
              <li>• Border animations with scale transforms</li>
            </ul>
          </div>
        </div>

        <div className="mt-8 p-6 rounded-lg" style={{ backgroundColor: '#1a1a2e' }}>
          <h3 className="text-lg font-semibold text-white mb-4">
            Current Page: <span style={{ color: '#33c2cc' }}>{currentPage}</span>
          </h3>
          <p className="text-gray-300 text-sm">
            Click on navigation items to see the active state change. 
            The active item will have aqua color and underline.
          </p>
        </div>

        <div className="mt-8 p-6 rounded-lg border" style={{ 
          backgroundColor: '#0a0a0a', 
          borderColor: '#33c2cc' 
        }}>
          <h3 className="text-lg font-semibold mb-4" style={{ color: '#33c2cc' }}>
            Code Structure
          </h3>
          <pre className="text-xs text-gray-300 overflow-x-auto">
{`class NavbarItem extends Component {
  constructor(props) {
    super(props);
    this.state = {
      variant: 'Default',     // Default, Hover IN, Hover OUT
      isHovered: false,
      isActive: false
    };
  }

  getVariantStyles() {
    const variants = {
      Default: { /* normal styles */ },
      'Hover IN': { /* hover active styles */ },
      'Hover OUT': { /* transition out styles */ }
    };
    return variants[this.state.variant];
  }

  handleMouseEnter() {
    this.setState({ variant: 'Hover IN' });
  }

  handleMouseLeave() {
    this.setState({ variant: 'Hover OUT' });
    setTimeout(() => {
      if (!this.state.isHovered) {
        this.setState({ variant: 'Default' });
      }
    }, 400);
  }
}`}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default NavbarDemo;
