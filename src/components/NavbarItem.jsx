
import { Component } from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';

class NavbarItem extends Component {
  constructor(props) {
    super(props);

    // State untuk menangani variants seperti di Framer
    this.state = {
      variant: 'Default', // Default, Hover IN, Hover OUT
      isHovered: false,
      isActive: false
    };

    // Bind methods
    this.handleMouseEnter = this.handleMouseEnter.bind(this);
    this.handleMouseLeave = this.handleMouseLeave.bind(this);
    this.handleClick = this.handleClick.bind(this);
  }

  // Variants configuration seperti di Framer
  getVariantStyles() {
    const { variant, isActive } = this.state;
    const { isCurrentPage } = this.props;

    const variants = {
      Default: {
        text: {
          color: isCurrentPage ? '#33c2cc' : '#a3a3a3',
          transform: 'translateY(0px)',
          opacity: 1
        },
        border: {
          borderBottom: isCurrentPage ? '2px solid #33c2cc' : '2px solid transparent',
          transform: 'scaleX(0)',
          transformOrigin: 'center'
        },
        wrapper: {
          backgroundColor: 'transparent',
          borderRadius: '8px',
          padding: '8px 16px',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
        }
      },

      'Hover IN': {
        text: {
          color: '#ffffff',
          transform: 'translateY(-1px)',
          opacity: 1
        },
        border: {
          borderBottom: '2px solid #33c2cc',
          transform: 'scaleX(1)',
          transformOrigin: 'center'
        },
        wrapper: {
          backgroundColor: 'rgba(51, 194, 204, 0.1)',
          borderRadius: '8px',
          padding: '8px 16px',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
        }
      },

      'Hover OUT': {
        text: {
          color: isCurrentPage ? '#33c2cc' : '#a3a3a3',
          transform: 'translateY(0px)',
          opacity: 0.8
        },
        border: {
          borderBottom: isCurrentPage ? '2px solid #33c2cc' : '2px solid transparent',
          transform: 'scaleX(0)',
          transformOrigin: 'center'
        },
        wrapper: {
          backgroundColor: 'transparent',
          borderRadius: '8px',
          padding: '8px 16px',
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
        }
      }
    };

    return variants[variant] || variants.Default;
  }

  handleMouseEnter() {
    this.setState({
      variant: 'Hover IN',
      isHovered: true
    });
  }

  handleMouseLeave() {
    this.setState({
      variant: 'Hover OUT',
      isHovered: false
    });

    // Reset ke Default setelah animasi selesai
    setTimeout(() => {
      if (!this.state.isHovered) {
        this.setState({ variant: 'Default' });
      }
    }, 400);
  }

  handleClick() {
    this.setState({ isActive: true });

    // Reset active state setelah navigasi
    setTimeout(() => {
      this.setState({ isActive: false });
    }, 200);
  }

  render() {
    const { name, path, className = '', ...otherProps } = this.props;
    const styles = this.getVariantStyles();

    return (
      <div
        style={styles.wrapper}
        className={`relative inline-block ${className}`}
      >
        <Link
          to={path}
          className="relative block text-lg font-medium"
          style={{
            ...styles.text,
            textDecoration: 'none',
            position: 'relative',
            zIndex: 1
          }}
          onMouseEnter={this.handleMouseEnter}
          onMouseLeave={this.handleMouseLeave}
          onClick={this.handleClick}
          {...otherProps}
        >
          {name}

          {/* Border element untuk animasi */}
          <div
            style={{
              ...styles.border,
              position: 'absolute',
              bottom: '-2px',
              left: '0',
              right: '0',
              height: '2px',
              backgroundColor: '#33c2cc',
              borderRadius: '1px'
            }}
          />
        </Link>
      </div>
    );
  }
}

NavbarItem.propTypes = {
  name: PropTypes.string.isRequired,
  path: PropTypes.string.isRequired,
  className: PropTypes.string,
  isCurrentPage: PropTypes.bool
};

NavbarItem.defaultProps = {
  className: '',
  isCurrentPage: false
};

export default NavbarItem;
